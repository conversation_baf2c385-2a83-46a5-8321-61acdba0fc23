torch>=1.9.0
tqdm>=4.57.0
boto3>=1.17.15
jsonschema>=3.2.0
Deprecated>=1.2.11
scipy>=1.6.1
matplotlib>=3.3.4
psutil>=5.8.0
tensorboard>=2.4.1
# pinned because of a crash in Snyk
setuptools>=65.5.1,<67.0.0
torchvision>=0.10.0
torchmetrics==0.8
hydra-core>=1.2.0
onnxruntime>=1.15.0
# onnx 1.16.0 introduce IR 10 which is not yet supported by onnx runtime & graphsurgeon
onnx==1.15.0
pillow>=10.2.0
pip-tools>=6.12.1
einops==0.3.2
treelib==1.6.1
termcolor==1.1.0
packaging>=20.4
# not directly required, pinned by <PERSON><PERSON>k to avoid a vulnerability
wheel>=0.38.0
# not directly required, pinned by Snyk to avoid a vulnerability
pygments>=2.7.4
stringcase>=1.2.0
rapidfuzz
json-tricks==3.16.1
onnxsim>=0.4.3,<1.0
data-gradients~=0.3.1
albumentations~=1.3
# not directly required, pinned by <PERSON><PERSON><PERSON> to avoid a vulnerability
fonttools>=4.43.0
# not directly required, pinned by Snyk to avoid a vulnerability
werkzeug>=2.3.8
imagesize~=1.4.1
