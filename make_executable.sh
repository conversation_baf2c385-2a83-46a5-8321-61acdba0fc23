#!/bin/bash

# Make all Python scripts executable
echo "🔧 Making scripts executable..."

chmod +x setup_environment.py
chmod +x scripts/setup_custom_dataset.py
chmod +x scripts/create_training_config.py
chmod +x scripts/train_yolo_nas.py
chmod +x scripts/evaluate_and_export.py
chmod +x scripts/inference_demo.py

echo "✅ All scripts are now executable"

echo ""
echo "📋 Available scripts:"
echo "  setup_environment.py          - Setup UV environment and dependencies"
echo "  scripts/setup_custom_dataset.py   - Configure and validate custom dataset"
echo "  scripts/create_training_config.py - Generate training configurations"
echo "  scripts/train_yolo_nas.py         - Main training script"
echo "  scripts/evaluate_and_export.py    - Model evaluation and export"
echo "  scripts/inference_demo.py         - Run inference on sample images"

echo ""
echo "📖 Documentation:"
echo "  README_APPLE_SILICON.md       - Apple Silicon implementation overview"
echo "  YOLO_NAS_TRAINING_GUIDE.md    - Complete step-by-step training guide"

echo ""
echo "🚀 Quick start:"
echo "  1. python setup_environment.py"
echo "  2. source yolo-nas-training/bin/activate"
echo "  3. uv pip install -e ."
echo "  4. Follow the guide in YOLO_NAS_TRAINING_GUIDE.md"
