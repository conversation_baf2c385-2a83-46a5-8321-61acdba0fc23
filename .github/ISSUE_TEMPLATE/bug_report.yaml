name: 🐛 Bug Report
description: Create a report to help us reproduce and fix the bug

body:
- type: markdown
  attributes:
    value: >
      #### Before submitting a bug, please make sure the issue hasn't been already addressed by searching through [the existing and past issues](https://github.com/Deci-AI/super-gradients/issues?q=is%3Aissue+sort%3Acreated-desc+).
      
      #### You can also try using our AI helper to get a fast solution
      
      [![Tri asking our AI helper](https://github.com/Deci-AI/super-gradients/raw/master/documentation/assets/SG_img/try_ai.png)](https://docs.deci.ai/super-gradients/latest/documentation/source/welcome.html?autoClick=true)
- type: textarea
  attributes:
    label: 🐛 Describe the bug
    description: |
      Please provide a clear and concise description of what the bug is.

      If relevant, add a minimal example so that we can reproduce the error by running the code. It is very important for the snippet to be as succinct (minimal) as possible, so please take time to trim down any irrelevant code to help us debug efficiently. We are going to copy-paste your code and we expect to get the same result as you did: avoid any external data, and include the relevant imports, etc. For example:

      ``` python
      # All necessary imports at the beginning
      from super_gradients.common.object_names import Models
      from super_gradients.training import models

      # A succinct reproducing example trimmed down to the essential parts:
      model = models.get(Models.YOLO_NAS_L, pretrained_weights="coco")
      ...
      ```

      Please also paste or describe the results you observe instead of the expected results. If you observe an error, please paste the error message including the **full** traceback of the exception. It may be relevant to wrap error messages in ```` ```triple quotes blocks``` ````.
    placeholder: |
      A clear and concise description of what the bug is.

      ``` python
      # Sample code to reproduce the problem
      ```

      ```
      The error message you got, with the full traceback.
      ```
  validations:
    required: true

- type: textarea
  attributes:
    label: Versions
    description: |
      Please run the following and paste the output below.
      ```sh
      wget https://raw.githubusercontent.com/pytorch/pytorch/main/torch/utils/collect_env.py
      # For security purposes, please check the contents of collect_env.py before running it.
      python collect_env.py
      ```
  validations:
    required: true

- type: markdown
  attributes:
    value: >
      Thanks for contributing 🎉!
