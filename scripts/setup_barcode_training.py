#!/usr/bin/env python3
"""
Complete setup script for YOLO-NAS barcode detection training
Optimized for Apple Silicon (Mac Mini M4)
"""

import os
import sys
import yaml
import json
import argparse
from pathlib import Path
from datetime import datetime

def create_barcode_dataset_config(dataset_path: str, batch_size: int = 16) -> dict:
    """Create optimized dataset configuration for barcode detection"""
    
    config = {
        "train_dataset_params": {
            "data_dir": dataset_path,
            "images_dir": "images/train",
            "labels_dir": "labels/train",
            "classes": ["Barcode", "QR Code"],
            "input_dim": [416, 416],  # Optimized for the dataset's native resolution
            "cache_annotations": True,
            "ignore_empty_annotations": False,  # Keep empty annotations for hard negatives
            "transforms": [
                {
                    "DetectionMosaic": {
                        "input_dim": [416, 416],
                        "prob": 0.8  # Reduced for barcode detection
                    }
                },
                {
                    "DetectionRandomAffine": {
                        "degrees": 5.0,  # Reduced rotation for barcodes
                        "translate": 0.05,  # Reduced translation
                        "scales": [0.8, 1.2],  # Conservative scaling
                        "shear": 1.0,  # Minimal shear
                        "target_size": [416, 416],
                        "filter_box_candidates": True,
                        "wh_thr": 2,
                        "area_thr": 0.05,  # Lower threshold for small barcodes
                        "ar_thr": 30  # Higher aspect ratio for linear barcodes
                    }
                },
                {
                    "DetectionMixup": {
                        "input_dim": [416, 416],
                        "mixup_scale": [0.7, 1.3],
                        "prob": 0.3,  # Reduced for barcode clarity
                        "flip_prob": 0.3  # Reduced horizontal flip for barcodes
                    }
                },
                {
                    "DetectionHSV": {
                        "prob": 0.8,
                        "hgain": 3,  # Reduced color augmentation
                        "sgain": 15,  # Reduced saturation
                        "vgain": 15   # Reduced brightness variation
                    }
                },
                {
                    "DetectionHorizontalFlip": {
                        "prob": 0.3  # Reduced for barcode orientation
                    }
                },
                {
                    "DetectionPaddedRescale": {
                        "input_dim": [416, 416]
                    }
                },
                {
                    "DetectionTargetsFormatTransform": {
                        "input_dim": [416, 416],
                        "output_format": "LABEL_CXCYWH"
                    }
                }
            ]
        },
        
        "train_dataloader_params": {
            "batch_size": batch_size,
            "num_workers": 4,  # Optimized for Apple Silicon
            "shuffle": True,
            "drop_last": True,
            "pin_memory": True,
            "collate_fn": "DetectionCollateFN"
        },
        
        "val_dataset_params": {
            "data_dir": dataset_path,
            "images_dir": "images/valid",
            "labels_dir": "labels/valid",
            "classes": ["Barcode", "QR Code"],
            "input_dim": [416, 416],
            "cache_annotations": True,
            "ignore_empty_annotations": False,
            "transforms": [
                {
                    "DetectionPaddedRescale": {
                        "input_dim": [416, 416]
                    }
                },
                {
                    "DetectionTargetsFormatTransform": {
                        "input_dim": [416, 416],
                        "output_format": "LABEL_CXCYWH"
                    }
                }
            ]
        },
        
        "val_dataloader_params": {
            "batch_size": batch_size,
            "num_workers": 4,
            "drop_last": False,
            "pin_memory": True,
            "collate_fn": "DetectionCollateFN"
        },
        
        "_convert_": "all"
    }
    
    return config

def create_barcode_training_config(
    dataset_name: str = "barcodes",
    model_size: str = "s",
    max_epochs: int = 100,
    batch_size: int = 16,
    initial_lr: float = 1e-3
) -> dict:
    """Create training configuration optimized for barcode detection"""
    
    config = {
        "defaults": [
            {"training_hyperparams": "coco2017_yolo_nas_train_params"},
            {"dataset_params": f"custom/{dataset_name}_dataset_params"},
            {"checkpoint_params": "default_checkpoint_params"},
            {"arch_params": f"yolo_nas_{model_size}_arch_params"},
            "_self_",
            "variable_setup"
        ],
        
        "train_dataloader": "yolo_nas_train",
        "val_dataloader": "yolo_nas_val",
        
        "dataset_name": dataset_name,
        "num_classes": 2,  # Barcode and QR Code
        
        "architecture": f"yolo_nas_{model_size}",
        "arch_params": {
            "num_classes": 2
        },
        
        "load_checkpoint": False,
        "checkpoint_params": {
            "pretrained_weights": "coco"  # Start with COCO pretrained weights
        },
        
        "result_path": f"checkpoints/custom/{dataset_name}",
        "resume": False,
        
        "training_hyperparams": {
            "resume": False,
            "max_epochs": max_epochs,
            
            # Apple Silicon optimizations
            "mixed_precision": True,
            "sync_bn": False,
            "batch_accumulate": 1,
            
            # Learning rate configuration - optimized for barcode detection
            "initial_lr": initial_lr,
            "lr_mode": "CosineLRScheduler",
            "cosine_final_lr_ratio": 0.01,
            "lr_warmup_epochs": 5,  # Longer warmup for stability
            "warmup_mode": "LinearEpochLRWarmup",
            
            # Optimizer settings
            "optimizer": "AdamW",
            "optimizer_params": {
                "weight_decay": 0.0005  # Slightly higher for regularization
            },
            "zero_weight_decay_on_bias_and_bn": True,
            
            # EMA settings
            "ema": True,
            "ema_params": {
                "decay": 0.9999  # Higher EMA decay for stability
            },
            
            # Loss configuration
            "loss": "PPYoloELoss",
            "criterion_params": {
                "num_classes": 2,
                "reg_max": 16
            },
            
            # Validation metrics
            "valid_metrics_list": [
                {
                    "DetectionMetrics_050": {
                        "score_thres": 0.1,
                        "top_k_predictions": 300,
                        "num_cls": 2,
                        "normalize_targets": True,
                        "post_prediction_callback": {
                            "_target_": "super_gradients.training.models.detection_models.pp_yolo_e.PPYoloEPostPredictionCallback",
                            "score_threshold": 0.01,
                            "nms_top_k": 1000,
                            "max_predictions": 300,
                            "nms_threshold": 0.7
                        }
                    }
                },
                {
                    "DetectionMetrics_050_095": {
                        "score_thres": 0.1,
                        "top_k_predictions": 300,
                        "num_cls": 2,
                        "normalize_targets": True,
                        "post_prediction_callback": {
                            "_target_": "super_gradients.training.models.detection_models.pp_yolo_e.PPYoloEPostPredictionCallback",
                            "score_threshold": 0.01,
                            "nms_top_k": 1000,
                            "max_predictions": 300,
                            "nms_threshold": 0.7
                        }
                    }
                }
            ],
            
            "metric_to_watch": "mAP@0.50",
            "greater_metric_to_watch_is_better": True,
            
            # Checkpointing - save more frequently for barcode detection
            "save_ckpt_epoch_list": [10, 25, 50, 75],
            "save_model": True,
            
            # Early stopping
            "early_stop": {
                "patience": 15,
                "min_delta": 0.001
            },
            
            # Phase callbacks for monitoring
            "phase_callbacks": [
                {
                    "LRCallbackBase": {
                        "phase": "TRAIN_EPOCH_END",
                        "freq": 1
                    }
                }
            ]
        },
        
        # Multi-GPU settings (disabled for Apple Silicon)
        "multi_gpu": "Off",
        "num_gpus": 1,
        
        "experiment_suffix": "_barcode_apple_silicon",
        "experiment_name": f"yolo_nas_{model_size}_barcode_detection"
    }
    
    return config

def save_configs(dataset_config: dict, training_config: dict, dataset_name: str, model_size: str):
    """Save configuration files"""
    
    config_dir = Path("configs/custom")
    config_dir.mkdir(parents=True, exist_ok=True)
    
    # Save dataset config
    dataset_config_path = config_dir / f"{dataset_name}_dataset_params.yaml"
    with open(dataset_config_path, 'w') as f:
        yaml.dump(dataset_config, f, default_flow_style=False, indent=2)
    
    # Save training config
    training_config_path = config_dir / f"{dataset_name}_yolo_nas_{model_size}_training.yaml"
    with open(training_config_path, 'w') as f:
        yaml.dump(training_config, f, default_flow_style=False, indent=2)
    
    return dataset_config_path, training_config_path

def create_training_script(dataset_name: str, model_size: str):
    """Create a convenient training script"""
    
    script_content = f'''#!/bin/bash

# YOLO-NAS Barcode Detection Training Script
# Generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

echo "🚀 Starting YOLO-NAS Barcode Detection Training"
echo "Model: YOLO-NAS-{model_size.upper()}"
echo "Dataset: {dataset_name}"
echo "Optimized for Apple Silicon (Mac Mini M4)"
echo ""

# Check if UV is available
if ! command -v uv &> /dev/null; then
    echo "❌ UV package manager not found"
    echo "Please install UV: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Check MPS availability
uv run python -c "import torch; print('✅ MPS Available' if torch.backends.mps.is_available() else '❌ MPS Not Available')"

# Start training
uv run python scripts/train_yolo_nas.py \\
    --config "configs/custom/{dataset_name}_yolo_nas_{model_size}_training.yaml" \\
    --log-dir "logs" \\
    --experiment-name "barcode_detection_{model_size}"

echo ""
echo "🎉 Training completed!"
echo "📊 Check logs in: logs/"
echo "💾 Check checkpoints in: checkpoints/custom/{dataset_name}/"
echo "📈 Monitor with: tensorboard --logdir logs"
'''
    
    script_path = Path(f"train_barcode_{model_size}.sh")
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Make executable
    os.chmod(script_path, 0o755)
    
    return script_path

def main():
    parser = argparse.ArgumentParser(description="Setup YOLO-NAS barcode detection training")
    parser.add_argument("--dataset-path", default="datasets/barcodes_yolo", help="Path to YOLO format dataset")
    parser.add_argument("--dataset-name", default="barcodes", help="Dataset name")
    parser.add_argument("--model-size", choices=["s", "m", "l"], default="s", help="Model size")
    parser.add_argument("--max-epochs", type=int, default=100, help="Maximum training epochs")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size")
    parser.add_argument("--initial-lr", type=float, default=1e-3, help="Initial learning rate")
    
    args = parser.parse_args()
    
    print("🔧 Setting up YOLO-NAS Barcode Detection Training")
    print("=" * 60)
    print(f"📁 Dataset: {args.dataset_path}")
    print(f"🏷️  Dataset name: {args.dataset_name}")
    print(f"🏗️  Model: YOLO-NAS-{args.model_size.upper()}")
    print(f"🏃 Epochs: {args.max_epochs}")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"📈 Learning rate: {args.initial_lr}")
    
    # Check if dataset exists
    dataset_path = Path(args.dataset_path)
    if not dataset_path.exists():
        print(f"❌ Dataset not found: {dataset_path}")
        print("Please run the COCO to YOLO conversion first:")
        print("python scripts/convert_coco_to_yolo.py")
        sys.exit(1)
    
    # Create dataset configuration
    dataset_config = create_barcode_dataset_config(
        str(dataset_path.absolute()),
        args.batch_size
    )
    
    # Create training configuration
    training_config = create_barcode_training_config(
        args.dataset_name,
        args.model_size,
        args.max_epochs,
        args.batch_size,
        args.initial_lr
    )
    
    # Save configurations
    dataset_config_path, training_config_path = save_configs(
        dataset_config, training_config, args.dataset_name, args.model_size
    )
    
    # Create training script
    script_path = create_training_script(args.dataset_name, args.model_size)
    
    print(f"\n✅ Configuration files created:")
    print(f"  📄 Dataset config: {dataset_config_path}")
    print(f"  📄 Training config: {training_config_path}")
    print(f"  📄 Training script: {script_path}")
    
    print(f"\n📋 Next steps:")
    print(f"1. Review the configurations if needed")
    print(f"2. Run training: ./{script_path}")
    print(f"3. Monitor with: tensorboard --logdir logs")
    
    print(f"\n🎯 Expected Performance for Barcode Detection:")
    print(f"  • Training time: ~2-3 hours on Mac Mini M4")
    print(f"  • Expected mAP@0.5: 85-95% (excellent for barcodes)")
    print(f"  • Inference speed: 20-30 FPS on Apple Silicon")

if __name__ == "__main__":
    main()
