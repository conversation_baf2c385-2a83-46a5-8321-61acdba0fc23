#!/usr/bin/env python3
"""
Custom Dataset Setup for YOLO-NAS Training
Configures dataset parameters and validates YOLO format
"""

import os
import yaml
import json
from pathlib import Path
from typing import List, Dict, Any
import argparse

class YOLODatasetValidator:
    """Validates YOLO format dataset structure and annotations"""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.errors = []
        self.warnings = []
        
    def validate_structure(self) -> bool:
        """Validate dataset directory structure"""
        print("🔍 Validating dataset structure...")
        
        required_dirs = [
            "images/train",
            "images/val", 
            "labels/train",
            "labels/val"
        ]
        
        for dir_path in required_dirs:
            full_path = self.dataset_path / dir_path
            if not full_path.exists():
                self.errors.append(f"Missing directory: {full_path}")
        
        if self.errors:
            return False
            
        print("✅ Dataset structure validation passed")
        return True
    
    def validate_annotations(self, classes: List[str]) -> Dict[str, Any]:
        """Validate YOLO format annotations"""
        print("🔍 Validating annotations...")
        
        stats = {
            "train_images": 0,
            "val_images": 0,
            "train_labels": 0,
            "val_labels": 0,
            "class_distribution": {cls: 0 for cls in classes},
            "issues": []
        }
        
        for split in ["train", "val"]:
            images_dir = self.dataset_path / "images" / split
            labels_dir = self.dataset_path / "labels" / split
            
            if not images_dir.exists() or not labels_dir.exists():
                continue
                
            image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
            label_files = list(labels_dir.glob("*.txt"))
            
            stats[f"{split}_images"] = len(image_files)
            stats[f"{split}_labels"] = len(label_files)
            
            # Check for missing labels
            for img_file in image_files:
                label_file = labels_dir / f"{img_file.stem}.txt"
                if not label_file.exists():
                    stats["issues"].append(f"Missing label for {img_file}")
            
            # Validate label format
            for label_file in label_files:
                try:
                    with open(label_file, 'r') as f:
                        lines = f.readlines()
                        for line_num, line in enumerate(lines, 1):
                            parts = line.strip().split()
                            if len(parts) != 5:
                                stats["issues"].append(
                                    f"Invalid format in {label_file}:{line_num} - Expected 5 values, got {len(parts)}"
                                )
                                continue
                            
                            try:
                                class_id = int(parts[0])
                                if 0 <= class_id < len(classes):
                                    stats["class_distribution"][classes[class_id]] += 1
                                else:
                                    stats["issues"].append(
                                        f"Invalid class ID {class_id} in {label_file}:{line_num}"
                                    )
                                
                                # Validate bbox coordinates
                                coords = [float(x) for x in parts[1:]]
                                for coord in coords:
                                    if not (0.0 <= coord <= 1.0):
                                        stats["issues"].append(
                                            f"Invalid coordinate {coord} in {label_file}:{line_num}"
                                        )
                            except ValueError as e:
                                stats["issues"].append(
                                    f"Invalid number format in {label_file}:{line_num}: {e}"
                                )
                                
                except Exception as e:
                    stats["issues"].append(f"Error reading {label_file}: {e}")
        
        return stats

def create_dataset_config(
    dataset_name: str,
    dataset_path: str,
    classes: List[str],
    input_size: List[int] = [640, 640],
    batch_size: int = 16
) -> Dict[str, Any]:
    """Create dataset configuration for YOLO-NAS"""
    
    config = {
        "train_dataset_params": {
            "data_dir": dataset_path,
            "images_dir": "images/train",
            "labels_dir": "labels/train", 
            "classes": classes,
            "input_dim": input_size,
            "cache_annotations": True,
            "ignore_empty_annotations": True,
            "transforms": [
                {
                    "DetectionMosaic": {
                        "input_dim": input_size,
                        "prob": 1.0
                    }
                },
                {
                    "DetectionRandomAffine": {
                        "degrees": 10.0,
                        "translate": 0.1,
                        "scales": [0.1, 2.0],
                        "shear": 2.0,
                        "target_size": input_size,
                        "filter_box_candidates": True,
                        "wh_thr": 2,
                        "area_thr": 0.1,
                        "ar_thr": 20
                    }
                },
                {
                    "DetectionMixup": {
                        "input_dim": input_size,
                        "mixup_scale": [0.5, 1.5],
                        "prob": 1.0,
                        "flip_prob": 0.5
                    }
                },
                {
                    "DetectionHSV": {
                        "prob": 1.0,
                        "hgain": 5,
                        "sgain": 30,
                        "vgain": 30
                    }
                },
                {
                    "DetectionHorizontalFlip": {
                        "prob": 0.5
                    }
                },
                {
                    "DetectionPaddedRescale": {
                        "input_dim": input_size
                    }
                },
                {
                    "DetectionTargetsFormatTransform": {
                        "input_dim": input_size,
                        "output_format": "LABEL_CXCYWH"
                    }
                }
            ]
        },
        
        "train_dataloader_params": {
            "batch_size": batch_size,
            "num_workers": 4,  # Reduced for Apple Silicon
            "shuffle": True,
            "drop_last": True,
            "pin_memory": True,
            "collate_fn": "DetectionCollateFN"
        },
        
        "val_dataset_params": {
            "data_dir": dataset_path,
            "images_dir": "images/val",
            "labels_dir": "labels/val",
            "classes": classes,
            "input_dim": input_size,
            "cache_annotations": True,
            "ignore_empty_annotations": True,
            "transforms": [
                {
                    "DetectionPaddedRescale": {
                        "input_dim": input_size
                    }
                },
                {
                    "DetectionTargetsFormatTransform": {
                        "input_dim": input_size,
                        "output_format": "LABEL_CXCYWH"
                    }
                }
            ]
        },
        
        "val_dataloader_params": {
            "batch_size": batch_size,
            "num_workers": 4,  # Reduced for Apple Silicon
            "drop_last": False,
            "pin_memory": True,
            "collate_fn": "DetectionCollateFN"
        },
        
        "_convert_": "all"
    }
    
    return config

def save_dataset_config(config: Dict[str, Any], output_path: str):
    """Save dataset configuration to YAML file"""
    with open(output_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)

def main():
    parser = argparse.ArgumentParser(description="Setup custom dataset for YOLO-NAS training")
    parser.add_argument("--dataset-name", required=True, help="Name of the dataset")
    parser.add_argument("--dataset-path", required=True, help="Path to dataset directory")
    parser.add_argument("--classes", required=True, nargs="+", help="List of class names")
    parser.add_argument("--input-size", nargs=2, type=int, default=[640, 640], help="Input image size [width, height]")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size for training")
    parser.add_argument("--validate", action="store_true", help="Validate dataset structure and annotations")
    
    args = parser.parse_args()
    
    print(f"🔧 Setting up dataset: {args.dataset_name}")
    print(f"📁 Dataset path: {args.dataset_path}")
    print(f"🏷️  Classes: {args.classes}")
    print(f"📐 Input size: {args.input_size}")
    print(f"📦 Batch size: {args.batch_size}")
    
    # Validate dataset if requested
    if args.validate:
        validator = YOLODatasetValidator(args.dataset_path)
        
        if not validator.validate_structure():
            print("❌ Dataset structure validation failed:")
            for error in validator.errors:
                print(f"  - {error}")
            return
        
        stats = validator.validate_annotations(args.classes)
        
        print("\n📊 Dataset Statistics:")
        print(f"  Train images: {stats['train_images']}")
        print(f"  Val images: {stats['val_images']}")
        print(f"  Train labels: {stats['train_labels']}")
        print(f"  Val labels: {stats['val_labels']}")
        
        print("\n📈 Class Distribution:")
        for cls, count in stats['class_distribution'].items():
            print(f"  {cls}: {count}")
        
        if stats['issues']:
            print(f"\n⚠️  Found {len(stats['issues'])} issues:")
            for issue in stats['issues'][:10]:  # Show first 10 issues
                print(f"  - {issue}")
            if len(stats['issues']) > 10:
                print(f"  ... and {len(stats['issues']) - 10} more issues")
    
    # Create dataset configuration
    config = create_dataset_config(
        args.dataset_name,
        args.dataset_path,
        args.classes,
        args.input_size,
        args.batch_size
    )
    
    # Save configuration
    config_dir = Path("configs/custom")
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_path = config_dir / f"{args.dataset_name}_dataset_params.yaml"
    save_dataset_config(config, str(config_path))
    
    print(f"\n✅ Dataset configuration saved to: {config_path}")
    print("\n📋 Next steps:")
    print("1. Review the generated configuration file")
    print("2. Adjust parameters if needed")
    print("3. Run the training script")

if __name__ == "__main__":
    main()
