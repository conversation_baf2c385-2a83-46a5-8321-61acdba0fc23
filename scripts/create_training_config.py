#!/usr/bin/env python3
"""
YOLO-NAS Training Configuration Generator
Optimized for Apple Silicon (Mac Mini M4) with MPS backend
"""

import yaml
import argparse
from pathlib import Path
from typing import Dict, Any, List

def create_apple_silicon_training_config(
    dataset_name: str,
    num_classes: int,
    dataset_config_path: str,
    max_epochs: int = 100,
    batch_size: int = 16,
    initial_lr: float = 5e-4,
    model_size: str = "s"  # s, m, l
) -> Dict[str, Any]:
    """Create training configuration optimized for Apple Silicon"""
    
    config = {
        "defaults": [
            {"training_hyperparams": "coco2017_yolo_nas_train_params"},
            {"dataset_params": f"custom/{dataset_name}_dataset_params"},
            {"checkpoint_params": "default_checkpoint_params"},
            {"arch_params": f"yolo_nas_{model_size}_arch_params"},
            "_self_",
            "variable_setup"
        ],
        
        "train_dataloader": "yolo_nas_train",
        "val_dataloader": "yolo_nas_val",
        
        "dataset_name": dataset_name,
        "num_classes": num_classes,
        
        "architecture": f"yolo_nas_{model_size}",
        "arch_params": {
            "num_classes": num_classes
        },
        
        "load_checkpoint": False,
        "checkpoint_params": {
            "pretrained_weights": "coco"
        },
        
        "result_path": f"checkpoints/custom/{dataset_name}",
        "resume": False,
        
        "training_hyperparams": {
            "resume": False,
            "max_epochs": max_epochs,
            
            # Apple Silicon optimizations
            "mixed_precision": True,  # Enable for better performance
            "sync_bn": False,  # Disable for single GPU training
            "batch_accumulate": 1,
            
            # Learning rate configuration
            "initial_lr": initial_lr,
            "lr_mode": "CosineLRScheduler",
            "cosine_final_lr_ratio": 0.1,
            "lr_warmup_epochs": 3,
            "warmup_mode": "LinearEpochLRWarmup",
            
            # Optimizer settings
            "optimizer": "AdamW",
            "optimizer_params": {
                "weight_decay": 0.0001
            },
            "zero_weight_decay_on_bias_and_bn": True,
            
            # EMA settings
            "ema": True,
            "ema_params": {
                "decay": 0.9
            },
            
            # Loss configuration
            "loss": "PPYoloELoss",
            "criterion_params": {
                "num_classes": num_classes,
                "reg_max": 16
            },
            
            # Validation metrics
            "valid_metrics_list": [
                {
                    "DetectionMetrics_050": {
                        "score_thres": 0.1,
                        "top_k_predictions": 300,
                        "num_cls": num_classes,
                        "normalize_targets": True,
                        "post_prediction_callback": {
                            "_target_": "super_gradients.training.models.detection_models.pp_yolo_e.PPYoloEPostPredictionCallback",
                            "score_threshold": 0.01,
                            "nms_top_k": 1000,
                            "max_predictions": 300,
                            "nms_threshold": 0.7
                        }
                    }
                }
            ],
            
            "metric_to_watch": "mAP@0.50",
            "greater_metric_to_watch_is_better": True,
            
            # Checkpointing
            "save_ckpt_epoch_list": [25, 50, 75],
            "save_model": True,
            
            # Phase callbacks for monitoring
            "phase_callbacks": []
        },
        
        # Multi-GPU settings (disabled for Apple Silicon)
        "multi_gpu": "Off",
        "num_gpus": 1,
        
        "experiment_suffix": "_apple_silicon",
        "experiment_name": f"yolo_nas_{model_size}_{dataset_name}_apple_silicon"
    }
    
    return config

def create_apple_silicon_arch_params(model_size: str, num_classes: int) -> Dict[str, Any]:
    """Create architecture parameters optimized for Apple Silicon"""
    
    # Base configuration for YOLO-NAS-S (can be scaled for M/L)
    size_configs = {
        "s": {
            "stem_channels": 48,
            "stage_channels": [96, 192, 384, 768],
            "stage_blocks": [2, 3, 5, 2],
            "neck_channels": [192, 96, 192, 384],
            "head_channels": [128, 256, 512],
            "width_mult": 0.5
        },
        "m": {
            "stem_channels": 64,
            "stage_channels": [128, 256, 512, 1024],
            "stage_blocks": [3, 4, 6, 3],
            "neck_channels": [256, 128, 256, 512],
            "head_channels": [192, 384, 768],
            "width_mult": 0.75
        },
        "l": {
            "stem_channels": 80,
            "stage_channels": [160, 320, 640, 1280],
            "stage_blocks": [3, 4, 6, 3],
            "neck_channels": [320, 160, 320, 640],
            "head_channels": [256, 512, 1024],
            "width_mult": 1.0
        }
    }
    
    config = size_configs[model_size]
    
    arch_params = {
        "in_channels": 3,
        
        "backbone": {
            "NStageBackbone": {
                "stem": {
                    "YoloNASStem": {
                        "out_channels": config["stem_channels"]
                    }
                },
                
                "stages": [
                    {
                        "YoloNASStage": {
                            "out_channels": config["stage_channels"][i],
                            "num_blocks": config["stage_blocks"][i],
                            "activation_type": "relu",
                            "hidden_channels": config["stage_channels"][i] // 3,
                            "concat_intermediates": False
                        }
                    } for i in range(4)
                ],
                
                "context_module": {
                    "SPP": {
                        "output_channels": config["stage_channels"][-1],
                        "activation_type": "relu",
                        "k": [5, 9, 13]
                    }
                },
                
                "out_layers": ["stage1", "stage2", "stage3", "context_module"]
            }
        },
        
        "neck": {
            "YoloNASPANNeckWithC2": {
                "neck1": {
                    "YoloNASUpStage": {
                        "out_channels": config["neck_channels"][0],
                        "num_blocks": 2,
                        "hidden_channels": config["neck_channels"][0] // 3,
                        "width_mult": 1,
                        "depth_mult": 1,
                        "activation_type": "relu",
                        "reduce_channels": True
                    }
                },
                "neck2": {
                    "YoloNASUpStage": {
                        "out_channels": config["neck_channels"][1],
                        "num_blocks": 2,
                        "hidden_channels": config["neck_channels"][1],
                        "width_mult": 1,
                        "depth_mult": 1,
                        "activation_type": "relu",
                        "reduce_channels": True
                    }
                },
                "neck3": {
                    "YoloNASDownStage": {
                        "out_channels": config["neck_channels"][2],
                        "num_blocks": 2,
                        "hidden_channels": config["neck_channels"][2] // 3,
                        "activation_type": "relu",
                        "width_mult": 1,
                        "depth_mult": 1
                    }
                },
                "neck4": {
                    "YoloNASDownStage": {
                        "out_channels": config["neck_channels"][3],
                        "num_blocks": 2,
                        "hidden_channels": config["neck_channels"][3] // 6,
                        "activation_type": "relu",
                        "width_mult": 1,
                        "depth_mult": 1
                    }
                }
            }
        },
        
        "heads": {
            "NDFLHeads": {
                "num_classes": num_classes,
                "reg_max": 16,
                "heads_list": [
                    {
                        "YoloNASDFLHead": {
                            "inter_channels": config["head_channels"][i],
                            "width_mult": config["width_mult"],
                            "first_conv_group_size": 0,
                            "stride": 8 * (2 ** i)
                        }
                    } for i in range(3)
                ]
            }
        },
        
        # Apple Silicon optimizations
        "bn_eps": 1e-3,
        "bn_momentum": 0.03,
        "inplace_act": True,
        
        "_convert_": "all"
    }
    
    return arch_params

def main():
    parser = argparse.ArgumentParser(description="Create YOLO-NAS training configuration for Apple Silicon")
    parser.add_argument("--dataset-name", required=True, help="Name of the dataset")
    parser.add_argument("--num-classes", type=int, required=True, help="Number of classes in dataset")
    parser.add_argument("--model-size", choices=["s", "m", "l"], default="s", help="Model size (s/m/l)")
    parser.add_argument("--max-epochs", type=int, default=100, help="Maximum training epochs")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size")
    parser.add_argument("--initial-lr", type=float, default=5e-4, help="Initial learning rate")
    parser.add_argument("--dataset-config", help="Path to dataset configuration file")

    args = parser.parse_args()

    print(f"🔧 Creating training configuration for YOLO-NAS-{args.model_size.upper()}")
    print(f"📊 Dataset: {args.dataset_name} ({args.num_classes} classes)")
    print(f"🏃 Training: {args.max_epochs} epochs, batch size {args.batch_size}")
    print(f"📈 Learning rate: {args.initial_lr}")

    # Create training configuration
    training_config = create_apple_silicon_training_config(
        args.dataset_name,
        args.num_classes,
        args.dataset_config or f"custom/{args.dataset_name}_dataset_params",
        args.max_epochs,
        args.batch_size,
        args.initial_lr,
        args.model_size
    )

    # Create architecture parameters
    arch_config = create_apple_silicon_arch_params(args.model_size, args.num_classes)

    # Save configurations
    config_dir = Path("configs/custom")
    config_dir.mkdir(parents=True, exist_ok=True)

    # Save training config
    training_config_path = config_dir / f"{args.dataset_name}_yolo_nas_{args.model_size}_apple_silicon.yaml"
    with open(training_config_path, 'w') as f:
        yaml.dump(training_config, f, default_flow_style=False, indent=2)

    # Save architecture config
    arch_config_path = config_dir / f"{args.dataset_name}_yolo_nas_{args.model_size}_arch_params.yaml"
    with open(arch_config_path, 'w') as f:
        yaml.dump(arch_config, f, default_flow_style=False, indent=2)

    print(f"\n✅ Training configuration saved to: {training_config_path}")
    print(f"✅ Architecture configuration saved to: {arch_config_path}")

    print("\n📋 Next steps:")
    print("1. Review and adjust the generated configurations")
    print("2. Ensure your dataset is properly formatted")
    print("3. Run training with: python scripts/train_yolo_nas.py")
    print("4. Monitor training with: tensorboard --logdir logs")

if __name__ == "__main__":
    main()
