#!/usr/bin/env python3
"""
Convert COCO format barcode dataset to YOLO format for YOLO-NAS training
Specialized for the Barcodes.v5i.coco dataset
"""

import json
import os
import shutil
from pathlib import Path
import argparse
from typing import Dict, List, Tu<PERSON>

def convert_bbox_coco_to_yolo(bbox: List[float], img_width: int, img_height: int) -> Tuple[float, float, float, float]:
    """
    Convert COCO bbox format [x, y, width, height] to YOLO format [center_x, center_y, width, height]
    All values normalized to [0, 1]
    """
    x, y, w, h = bbox
    
    # Convert to center coordinates
    center_x = x + w / 2
    center_y = y + h / 2
    
    # Normalize to [0, 1]
    center_x_norm = center_x / img_width
    center_y_norm = center_y / img_height
    width_norm = w / img_width
    height_norm = h / img_height
    
    return center_x_norm, center_y_norm, width_norm, height_norm

def convert_coco_split_to_yolo(coco_dir: str, split: str, output_dir: str, class_mapping: Dict[int, int]):
    """Convert a single COCO split (train/valid/test) to YOLO format"""
    
    print(f"🔄 Converting {split} split...")
    
    # Paths
    coco_images_dir = Path(coco_dir) / split
    coco_annotations_file = coco_images_dir / "_annotations.coco.json"
    
    output_images_dir = Path(output_dir) / "images" / split
    output_labels_dir = Path(output_dir) / "labels" / split
    
    # Create output directories
    output_images_dir.mkdir(parents=True, exist_ok=True)
    output_labels_dir.mkdir(parents=True, exist_ok=True)
    
    # Load COCO annotations
    with open(coco_annotations_file, 'r') as f:
        coco_data = json.load(f)
    
    # Create image ID to filename mapping
    images = {img['id']: img for img in coco_data['images']}
    
    # Group annotations by image
    annotations_by_image = {}
    for ann in coco_data['annotations']:
        image_id = ann['image_id']
        if image_id not in annotations_by_image:
            annotations_by_image[image_id] = []
        annotations_by_image[image_id].append(ann)
    
    # Convert each image and its annotations
    converted_images = 0
    converted_annotations = 0
    
    for image_id, image_info in images.items():
        # Copy image file
        src_image_path = coco_images_dir / image_info['file_name']
        dst_image_path = output_images_dir / image_info['file_name']
        
        if src_image_path.exists():
            shutil.copy2(src_image_path, dst_image_path)
            converted_images += 1
            
            # Create YOLO label file
            label_filename = Path(image_info['file_name']).stem + '.txt'
            label_path = output_labels_dir / label_filename
            
            # Convert annotations for this image
            yolo_annotations = []
            if image_id in annotations_by_image:
                for ann in annotations_by_image[image_id]:
                    # Map COCO category ID to YOLO class ID
                    coco_category_id = ann['category_id']
                    if coco_category_id in class_mapping:
                        yolo_class_id = class_mapping[coco_category_id]
                        
                        # Convert bbox
                        bbox = ann['bbox']
                        center_x, center_y, width, height = convert_bbox_coco_to_yolo(
                            bbox, image_info['width'], image_info['height']
                        )
                        
                        # Create YOLO annotation line
                        yolo_line = f"{yolo_class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"
                        yolo_annotations.append(yolo_line)
                        converted_annotations += 1
            
            # Write YOLO label file
            with open(label_path, 'w') as f:
                f.write('\n'.join(yolo_annotations))
                if yolo_annotations:  # Add final newline if there are annotations
                    f.write('\n')
        else:
            print(f"⚠️  Warning: Image file not found: {src_image_path}")
    
    print(f"✅ {split}: {converted_images} images, {converted_annotations} annotations")
    return converted_images, converted_annotations

def create_dataset_yaml(output_dir: str, class_names: List[str]):
    """Create dataset.yaml file for YOLO-NAS training"""
    
    dataset_yaml = {
        'path': str(Path(output_dir).absolute()),
        'train': 'images/train',
        'val': 'images/valid',
        'test': 'images/test',
        'nc': len(class_names),
        'names': class_names
    }
    
    yaml_path = Path(output_dir) / "dataset.yaml"
    
    # Write YAML file
    with open(yaml_path, 'w') as f:
        f.write(f"# Barcode Detection Dataset\n")
        f.write(f"# Converted from COCO format to YOLO format\n")
        f.write(f"# Classes: {', '.join(class_names)}\n\n")
        
        f.write(f"path: {dataset_yaml['path']}\n")
        f.write(f"train: {dataset_yaml['train']}\n")
        f.write(f"val: {dataset_yaml['val']}\n")
        f.write(f"test: {dataset_yaml['test']}\n\n")
        
        f.write(f"nc: {dataset_yaml['nc']}\n")
        f.write(f"names:\n")
        for i, name in enumerate(class_names):
            f.write(f"  {i}: {name}\n")
    
    print(f"✅ Dataset YAML created: {yaml_path}")
    return yaml_path

def analyze_dataset_statistics(output_dir: str, class_names: List[str]):
    """Analyze and print dataset statistics"""
    
    print("\n📊 Dataset Statistics:")
    print("=" * 50)
    
    total_images = 0
    total_annotations = 0
    class_counts = {name: 0 for name in class_names}
    
    for split in ['train', 'valid', 'test']:
        labels_dir = Path(output_dir) / "labels" / split
        images_dir = Path(output_dir) / "images" / split
        
        if not labels_dir.exists():
            continue
            
        split_images = len(list(images_dir.glob("*.jpg"))) + len(list(images_dir.glob("*.png")))
        split_annotations = 0
        split_class_counts = {name: 0 for name in class_names}
        
        for label_file in labels_dir.glob("*.txt"):
            with open(label_file, 'r') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip():
                        class_id = int(line.split()[0])
                        if 0 <= class_id < len(class_names):
                            split_class_counts[class_names[class_id]] += 1
                            class_counts[class_names[class_id]] += 1
                            split_annotations += 1
        
        total_images += split_images
        total_annotations += split_annotations
        
        print(f"{split.upper()} SET:")
        print(f"  Images: {split_images:,}")
        print(f"  Annotations: {split_annotations:,}")
        for class_name, count in split_class_counts.items():
            print(f"  {class_name}: {count:,}")
        print()
    
    print("TOTAL DATASET:")
    print(f"  Images: {total_images:,}")
    print(f"  Annotations: {total_annotations:,}")
    for class_name, count in class_counts.items():
        percentage = (count / total_annotations * 100) if total_annotations > 0 else 0
        print(f"  {class_name}: {count:,} ({percentage:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description="Convert COCO barcode dataset to YOLO format")
    parser.add_argument("--input-dir", default="Barcodes.v5i.coco", help="Input COCO dataset directory")
    parser.add_argument("--output-dir", default="datasets/barcodes_yolo", help="Output YOLO dataset directory")
    parser.add_argument("--class-mapping", help="Custom class mapping (JSON format)")
    
    args = parser.parse_args()
    
    print("🔄 Converting COCO Barcode Dataset to YOLO Format")
    print("=" * 60)
    
    # Load COCO categories to understand the class structure
    coco_annotations_file = Path(args.input_dir) / "train" / "_annotations.coco.json"
    with open(coco_annotations_file, 'r') as f:
        coco_data = json.load(f)
    
    categories = coco_data['categories']
    print("📋 Original COCO Categories:")
    for cat in categories:
        print(f"  ID {cat['id']}: {cat['name']} (supercategory: {cat['supercategory']})")
    
    # Create class mapping (skip the parent "Barcodes" category, use only specific types)
    if args.class_mapping:
        with open(args.class_mapping, 'r') as f:
            class_mapping = json.load(f)
    else:
        # Default mapping: skip parent category "Barcodes" (id=0), map specific types
        class_mapping = {
            1: 0,  # "Barcode" -> class 0
            2: 1,  # "QR Code" -> class 1
        }
    
    # Create class names list
    class_names = ["Barcode", "QR Code"]
    
    print(f"\n🎯 YOLO Class Mapping:")
    for coco_id, yolo_id in class_mapping.items():
        coco_name = next(cat['name'] for cat in categories if cat['id'] == coco_id)
        print(f"  COCO ID {coco_id} ({coco_name}) -> YOLO Class {yolo_id} ({class_names[yolo_id]})")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Convert each split
    total_images = 0
    total_annotations = 0
    
    for split in ['train', 'valid', 'test']:
        split_dir = Path(args.input_dir) / split
        if split_dir.exists():
            images, annotations = convert_coco_split_to_yolo(
                args.input_dir, split, args.output_dir, class_mapping
            )
            total_images += images
            total_annotations += annotations
    
    # Create dataset.yaml
    create_dataset_yaml(args.output_dir, class_names)
    
    # Analyze statistics
    analyze_dataset_statistics(args.output_dir, class_names)
    
    print(f"\n🎉 Conversion completed successfully!")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"📊 Total: {total_images:,} images, {total_annotations:,} annotations")
    print(f"\n📋 Next steps:")
    print(f"1. Review the converted dataset in: {output_dir}")
    print(f"2. Run dataset validation: python scripts/setup_custom_dataset.py --validate")
    print(f"3. Create training configuration")
    print(f"4. Start training!")

if __name__ == "__main__":
    main()
