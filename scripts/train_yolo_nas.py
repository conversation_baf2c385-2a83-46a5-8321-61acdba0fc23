#!/usr/bin/env python3
"""
YOLO-NAS Training Script for Apple Silicon
Optimized for Mac Mini M4 with MPS backend
"""

import os
import sys
import torch
import argparse
import logging
from pathlib import Path
from datetime import datetime
import warnings

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import SuperGradients
try:
    from super_gradients import init_trainer, Trainer
    from super_gradients.training import models
    from super_gradients.common.object_names import Models
    from super_gradients.training.utils.distributed_training_utils import setup_device
    from super_gradients.common import MultiGPUMode
except ImportError as e:
    print(f"❌ Error importing SuperGradients: {e}")
    print("Please ensure SuperGradients is installed: uv pip install -e .")
    sys.exit(1)

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

def setup_logging(log_dir: str, experiment_name: str):
    """Setup logging configuration"""
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / f"{experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def check_apple_silicon_setup():
    """Check and configure Apple Silicon optimizations"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Checking Apple Silicon setup...")
    
    # Check PyTorch version
    logger.info(f"PyTorch version: {torch.__version__}")
    
    # Check MPS availability
    if torch.backends.mps.is_available():
        logger.info("✅ MPS (Metal Performance Shaders) is available")
        
        # Test MPS functionality
        try:
            device = torch.device("mps")
            test_tensor = torch.randn(10, 10).to(device)
            result = torch.mm(test_tensor, test_tensor)
            logger.info("✅ MPS functionality test passed")
            return True
        except Exception as e:
            logger.warning(f"⚠️  MPS test failed: {e}")
            logger.info("Falling back to CPU")
            return False
    else:
        logger.warning("⚠️  MPS is not available, using CPU")
        return False

def optimize_for_apple_silicon():
    """Apply Apple Silicon specific optimizations"""
    logger = logging.getLogger(__name__)
    
    # Set environment variables for optimal performance
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"
    
    # Configure memory management
    if torch.backends.mps.is_available():
        # Enable memory efficient attention if available
        try:
            torch.backends.mps.empty_cache()
            logger.info("✅ MPS memory cache cleared")
        except:
            pass
    
    # Set optimal number of threads for Apple Silicon
    torch.set_num_threads(8)  # Optimal for M4 chip
    
    logger.info("✅ Apple Silicon optimizations applied")

def load_training_config(config_path: str):
    """Load training configuration from YAML file"""
    import yaml
    
    config_path = Path(config_path)
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    return config

def setup_trainer(experiment_name: str, checkpoint_dir: str):
    """Setup SuperGradients trainer"""
    logger = logging.getLogger(__name__)
    
    # Initialize trainer
    init_trainer()
    
    # Setup device for single GPU/MPS training
    if torch.backends.mps.is_available():
        setup_device(multi_gpu=MultiGPUMode.OFF, num_gpus=1)
        logger.info("🔧 Configured for MPS training")
    else:
        setup_device(multi_gpu=MultiGPUMode.OFF, num_gpus=0)
        logger.info("🔧 Configured for CPU training")
    
    # Create trainer
    trainer = Trainer(experiment_name=experiment_name, ckpt_root_dir=checkpoint_dir)
    
    return trainer

def train_model(config_path: str, resume_from: str = None):
    """Main training function"""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting YOLO-NAS training on Apple Silicon")
    
    # Load configuration
    try:
        config = load_training_config(config_path)
        logger.info(f"✅ Configuration loaded from: {config_path}")
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {e}")
        return False
    
    # Extract configuration parameters
    experiment_name = config.get("experiment_name", "yolo_nas_custom")
    architecture = config.get("architecture", "yolo_nas_s")
    num_classes = config["arch_params"]["num_classes"]
    checkpoint_dir = config.get("result_path", "checkpoints/custom")
    
    logger.info(f"📊 Experiment: {experiment_name}")
    logger.info(f"🏗️  Architecture: {architecture}")
    logger.info(f"🏷️  Classes: {num_classes}")
    
    # Setup trainer
    try:
        trainer = setup_trainer(experiment_name, checkpoint_dir)
        logger.info("✅ Trainer initialized")
    except Exception as e:
        logger.error(f"❌ Failed to setup trainer: {e}")
        return False
    
    # Load model
    try:
        model = models.get(
            model_name=architecture,
            arch_params=config["arch_params"],
            num_classes=num_classes,
            pretrained_weights=config["checkpoint_params"].get("pretrained_weights", "coco")
        )
        logger.info(f"✅ Model loaded: {architecture}")
        
        # Move model to appropriate device
        if torch.backends.mps.is_available():
            model = model.to("mps")
            logger.info("📱 Model moved to MPS device")
        
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        return False
    
    # Setup training parameters
    training_params = config["training_hyperparams"].copy()
    
    # Add resume capability
    if resume_from:
        training_params["resume"] = True
        training_params["resume_path"] = resume_from
        logger.info(f"🔄 Resuming training from: {resume_from}")
    
    # Start training
    try:
        logger.info("🏃 Starting training...")
        trainer.train(
            model=model,
            training_params=training_params,
            train_loader=config.get("train_dataloader"),
            valid_loader=config.get("val_dataloader")
        )
        logger.info("✅ Training completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Train YOLO-NAS on Apple Silicon")
    parser.add_argument("--config", required=True, help="Path to training configuration file")
    parser.add_argument("--resume", help="Path to checkpoint to resume from")
    parser.add_argument("--log-dir", default="logs", help="Directory for log files")
    parser.add_argument("--experiment-name", help="Override experiment name")
    
    args = parser.parse_args()
    
    # Setup logging
    experiment_name = args.experiment_name or f"yolo_nas_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    logger = setup_logging(args.log_dir, experiment_name)
    
    logger.info("=" * 60)
    logger.info("🍎 YOLO-NAS Training on Apple Silicon")
    logger.info("=" * 60)
    
    # Check Apple Silicon setup
    mps_available = check_apple_silicon_setup()
    
    # Apply optimizations
    optimize_for_apple_silicon()
    
    # Start training
    success = train_model(args.config, args.resume)
    
    if success:
        logger.info("🎉 Training completed successfully!")
        logger.info("📋 Next steps:")
        logger.info("1. Evaluate the trained model")
        logger.info("2. Export to ONNX/CoreML for deployment")
        logger.info("3. Run inference tests")
    else:
        logger.error("❌ Training failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
