#!/usr/bin/env python3
"""
YOLO-NAS Model Evaluation and Export Script
Optimized for Apple Silicon deployment
"""

import os
import sys
import torch
import argparse
import logging
import json
import time
from pathlib import Path
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from super_gradients import init_trainer, Trainer
    from super_gradients.training import models
    from super_gradients.training.metrics import DetectionMetrics
    from super_gradients.training.utils.distributed_training_utils import setup_device
    from super_gradients.common import MultiGPUMode
except ImportError as e:
    print(f"❌ Error importing SuperGradients: {e}")
    sys.exit(1)

def setup_logging(log_dir: str, experiment_name: str):
    """Setup logging configuration"""
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / f"{experiment_name}_eval_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def load_trained_model(checkpoint_path: str, architecture: str, num_classes: int):
    """Load trained YOLO-NAS model from checkpoint"""
    logger = logging.getLogger(__name__)
    
    try:
        # Load model architecture
        model = models.get(
            model_name=architecture,
            num_classes=num_classes,
            checkpoint_path=checkpoint_path
        )
        
        # Move to appropriate device
        if torch.backends.mps.is_available():
            model = model.to("mps")
            logger.info("📱 Model loaded on MPS device")
        else:
            model = model.to("cpu")
            logger.info("💻 Model loaded on CPU")
        
        model.eval()
        logger.info(f"✅ Model loaded from: {checkpoint_path}")
        return model
        
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        return None

def evaluate_model(model, val_dataloader, class_names: list):
    """Evaluate model performance on validation set"""
    logger = logging.getLogger(__name__)
    
    logger.info("📊 Starting model evaluation...")
    
    # Initialize metrics
    metrics = DetectionMetrics(
        score_thres=0.1,
        top_k_predictions=300,
        num_cls=len(class_names),
        normalize_targets=True
    )
    
    total_time = 0
    num_batches = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_dataloader):
            start_time = time.time()
            
            # Forward pass
            predictions = model(batch[0])
            
            # Calculate inference time
            inference_time = time.time() - start_time
            total_time += inference_time
            num_batches += 1
            
            # Update metrics
            metrics.update(predictions, batch[1])
            
            if batch_idx % 10 == 0:
                logger.info(f"Processed batch {batch_idx}/{len(val_dataloader)}")
    
    # Compute final metrics
    results = metrics.compute()
    avg_inference_time = total_time / num_batches
    
    logger.info("📈 Evaluation Results:")
    logger.info(f"  mAP@0.5: {results.get('mAP@0.50', 0):.4f}")
    logger.info(f"  mAP@0.5:0.95: {results.get('mAP@0.50:0.95', 0):.4f}")
    logger.info(f"  Average inference time: {avg_inference_time:.4f}s")
    logger.info(f"  FPS: {1/avg_inference_time:.2f}")
    
    return results, avg_inference_time

def export_to_onnx(model, output_path: str, input_size: tuple = (640, 640)):
    """Export model to ONNX format"""
    logger = logging.getLogger(__name__)
    
    try:
        # Prepare model for export
        model.eval()
        model.prep_model_for_conversion(input_size=[1, 3, input_size[0], input_size[1]])
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, input_size[0], input_size[1])
        if torch.backends.mps.is_available():
            dummy_input = dummy_input.to("mps")
        
        # Export to ONNX
        torch.onnx.export(
            model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        logger.info(f"✅ Model exported to ONNX: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ ONNX export failed: {e}")
        return False

def export_to_coreml(model, output_path: str, input_size: tuple = (640, 640)):
    """Export model to CoreML format for Apple ecosystem"""
    logger = logging.getLogger(__name__)
    
    try:
        import coremltools as ct
        
        # Prepare model for export
        model.eval()
        model.prep_model_for_conversion(input_size=[1, 3, input_size[0], input_size[1]])
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, input_size[0], input_size[1])
        
        # Trace the model
        traced_model = torch.jit.trace(model, dummy_input)
        
        # Convert to CoreML
        coreml_model = ct.convert(
            traced_model,
            inputs=[ct.ImageType(
                name="input",
                shape=dummy_input.shape,
                bias=[-0.485/0.229, -0.456/0.224, -0.406/0.225],
                scale=[1/(0.229*255), 1/(0.224*255), 1/(0.225*255)]
            )]
        )
        
        # Save CoreML model
        coreml_model.save(output_path)
        
        logger.info(f"✅ Model exported to CoreML: {output_path}")
        return True
        
    except ImportError:
        logger.warning("⚠️  CoreML Tools not available. Install with: pip install coremltools")
        return False
    except Exception as e:
        logger.error(f"❌ CoreML export failed: {e}")
        return False

def benchmark_inference(model, input_size: tuple = (640, 640), num_runs: int = 100):
    """Benchmark inference performance"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"⏱️  Benchmarking inference performance ({num_runs} runs)...")
    
    # Prepare dummy input
    dummy_input = torch.randn(1, 3, input_size[0], input_size[1])
    if torch.backends.mps.is_available():
        dummy_input = dummy_input.to("mps")
    
    model.eval()
    
    # Warmup runs
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # Benchmark runs
    times = []
    with torch.no_grad():
        for _ in range(num_runs):
            start_time = time.time()
            _ = model(dummy_input)
            end_time = time.time()
            times.append(end_time - start_time)
    
    # Calculate statistics
    mean_time = np.mean(times)
    std_time = np.std(times)
    min_time = np.min(times)
    max_time = np.max(times)
    fps = 1 / mean_time
    
    logger.info("📊 Inference Benchmark Results:")
    logger.info(f"  Mean inference time: {mean_time:.4f}s ± {std_time:.4f}s")
    logger.info(f"  Min inference time: {min_time:.4f}s")
    logger.info(f"  Max inference time: {max_time:.4f}s")
    logger.info(f"  Average FPS: {fps:.2f}")
    
    return {
        "mean_time": mean_time,
        "std_time": std_time,
        "min_time": min_time,
        "max_time": max_time,
        "fps": fps
    }

def create_evaluation_report(results: dict, benchmark_results: dict, output_path: str):
    """Create comprehensive evaluation report"""
    logger = logging.getLogger(__name__)
    
    report = {
        "evaluation_timestamp": datetime.now().isoformat(),
        "model_performance": {
            "mAP_50": float(results.get('mAP@0.50', 0)),
            "mAP_50_95": float(results.get('mAP@0.50:0.95', 0)),
        },
        "inference_performance": benchmark_results,
        "system_info": {
            "pytorch_version": torch.__version__,
            "mps_available": torch.backends.mps.is_available(),
            "device": "mps" if torch.backends.mps.is_available() else "cpu"
        }
    }
    
    # Save report
    with open(output_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📄 Evaluation report saved: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Evaluate and export YOLO-NAS model")
    parser.add_argument("--checkpoint", required=True, help="Path to trained model checkpoint")
    parser.add_argument("--architecture", default="yolo_nas_s", help="Model architecture")
    parser.add_argument("--num-classes", type=int, required=True, help="Number of classes")
    parser.add_argument("--class-names", nargs="+", required=True, help="List of class names")
    parser.add_argument("--input-size", nargs=2, type=int, default=[640, 640], help="Input image size")
    parser.add_argument("--export-dir", default="exports", help="Directory for exported models")
    parser.add_argument("--benchmark-runs", type=int, default=100, help="Number of benchmark runs")
    parser.add_argument("--skip-coreml", action="store_true", help="Skip CoreML export")
    
    args = parser.parse_args()
    
    # Setup logging
    experiment_name = f"eval_{Path(args.checkpoint).stem}"
    logger = setup_logging("logs", experiment_name)
    
    logger.info("=" * 60)
    logger.info("📊 YOLO-NAS Model Evaluation and Export")
    logger.info("=" * 60)
    
    # Initialize trainer
    init_trainer()
    setup_device(multi_gpu=MultiGPUMode.OFF, num_gpus=1 if torch.backends.mps.is_available() else 0)
    
    # Load model
    model = load_trained_model(args.checkpoint, args.architecture, args.num_classes)
    if model is None:
        sys.exit(1)
    
    # Benchmark inference
    benchmark_results = benchmark_inference(model, tuple(args.input_size), args.benchmark_runs)
    
    # Create export directory
    export_dir = Path(args.export_dir)
    export_dir.mkdir(parents=True, exist_ok=True)
    
    # Export to ONNX
    onnx_path = export_dir / f"{experiment_name}.onnx"
    export_to_onnx(model, str(onnx_path), tuple(args.input_size))
    
    # Export to CoreML (if not skipped)
    if not args.skip_coreml:
        coreml_path = export_dir / f"{experiment_name}.mlmodel"
        export_to_coreml(model, str(coreml_path), tuple(args.input_size))
    
    # Create evaluation report
    report_path = export_dir / f"{experiment_name}_report.json"
    create_evaluation_report({}, benchmark_results, str(report_path))
    
    logger.info("🎉 Evaluation and export completed!")

if __name__ == "__main__":
    main()
