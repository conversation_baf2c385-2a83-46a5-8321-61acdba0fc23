#!/usr/bin/env python3
"""
YOLO-NAS Inference Demo Script
Test trained model on sample images
"""

import os
import sys
import torch
import argparse
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from super_gradients import init_trainer
    from super_gradients.training import models
    from super_gradients.training.utils.distributed_training_utils import setup_device
    from super_gradients.common import MultiGPUMode
except ImportError as e:
    print(f"❌ Error importing SuperGradients: {e}")
    sys.exit(1)

def load_model(checkpoint_path: str, architecture: str, num_classes: int, class_names: list):
    """Load trained YOLO-NAS model"""
    print(f"🔄 Loading model from: {checkpoint_path}")
    
    # Initialize trainer
    init_trainer()
    setup_device(multi_gpu=MultiGPUMode.OFF, num_gpus=1 if torch.backends.mps.is_available() else 0)
    
    try:
        # Load model
        model = models.get(
            model_name=architecture,
            num_classes=num_classes,
            checkpoint_path=checkpoint_path
        )
        
        # Move to appropriate device
        if torch.backends.mps.is_available():
            model = model.to("mps")
            print("📱 Model loaded on MPS device")
        else:
            model = model.to("cpu")
            print("💻 Model loaded on CPU")
        
        model.eval()
        print("✅ Model loaded successfully")
        return model
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

def preprocess_image(image_path: str, input_size: tuple = (640, 640)):
    """Preprocess image for inference"""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Convert BGR to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_shape = image_rgb.shape[:2]
    
    # Resize image while maintaining aspect ratio
    h, w = original_shape
    scale = min(input_size[0] / w, input_size[1] / h)
    new_w, new_h = int(w * scale), int(h * scale)
    
    resized = cv2.resize(image_rgb, (new_w, new_h))
    
    # Pad to target size
    padded = np.full((input_size[1], input_size[0], 3), 114, dtype=np.uint8)
    padded[:new_h, :new_w] = resized
    
    # Normalize and convert to tensor
    normalized = padded.astype(np.float32) / 255.0
    tensor = torch.from_numpy(normalized).permute(2, 0, 1).unsqueeze(0)
    
    return tensor, original_shape, scale

def postprocess_predictions(predictions, original_shape: tuple, scale: float, 
                          confidence_threshold: float = 0.5, nms_threshold: float = 0.7):
    """Postprocess model predictions"""
    # Extract predictions (assuming standard YOLO-NAS output format)
    if hasattr(predictions, 'prediction'):
        pred = predictions.prediction
        boxes = pred.bboxes_xyxy
        scores = pred.confidence
        labels = pred.labels.astype(int)
    else:
        # Handle different prediction formats
        boxes = predictions[0][:, :4]  # x1, y1, x2, y2
        scores = predictions[0][:, 4]
        labels = predictions[0][:, 5].astype(int)
    
    # Filter by confidence
    valid_indices = scores >= confidence_threshold
    boxes = boxes[valid_indices]
    scores = scores[valid_indices]
    labels = labels[valid_indices]
    
    # Scale boxes back to original image size
    boxes[:, [0, 2]] = boxes[:, [0, 2]] / scale  # x coordinates
    boxes[:, [1, 3]] = boxes[:, [1, 3]] / scale  # y coordinates
    
    # Clip boxes to image boundaries
    boxes[:, [0, 2]] = np.clip(boxes[:, [0, 2]], 0, original_shape[1])
    boxes[:, [1, 3]] = np.clip(boxes[:, [1, 3]], 0, original_shape[0])
    
    return boxes, scores, labels

def visualize_predictions(image_path: str, boxes: np.ndarray, scores: np.ndarray, 
                         labels: np.ndarray, class_names: list, output_path: str = None):
    """Visualize predictions on image"""
    # Load original image
    image = Image.open(image_path)
    
    # Create figure and axis
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.imshow(image)
    
    # Define colors for different classes
    colors = plt.cm.Set3(np.linspace(0, 1, len(class_names)))
    
    # Draw bounding boxes
    for box, score, label in zip(boxes, scores, labels):
        x1, y1, x2, y2 = box
        width = x2 - x1
        height = y2 - y1
        
        # Create rectangle
        rect = patches.Rectangle(
            (x1, y1), width, height,
            linewidth=2,
            edgecolor=colors[label % len(colors)],
            facecolor='none'
        )
        ax.add_patch(rect)
        
        # Add label and confidence
        class_name = class_names[label] if label < len(class_names) else f"Class_{label}"
        label_text = f"{class_name}: {score:.2f}"
        
        ax.text(
            x1, y1 - 5,
            label_text,
            bbox=dict(boxstyle="round,pad=0.3", facecolor=colors[label % len(colors)], alpha=0.7),
            fontsize=10,
            color='black'
        )
    
    ax.set_xlim(0, image.width)
    ax.set_ylim(image.height, 0)
    ax.axis('off')
    ax.set_title(f"YOLO-NAS Predictions - {len(boxes)} detections", fontsize=14)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"💾 Visualization saved to: {output_path}")
    
    plt.show()

def run_inference(model, image_path: str, class_names: list, 
                 input_size: tuple = (640, 640), confidence_threshold: float = 0.5):
    """Run inference on a single image"""
    print(f"🔍 Running inference on: {image_path}")
    
    # Preprocess image
    tensor, original_shape, scale = preprocess_image(image_path, input_size)
    
    # Move tensor to appropriate device
    if torch.backends.mps.is_available():
        tensor = tensor.to("mps")
    
    # Run inference
    with torch.no_grad():
        start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        
        if start_time:
            start_time.record()
        
        predictions = model(tensor)
        
        if end_time:
            end_time.record()
            torch.cuda.synchronize()
            inference_time = start_time.elapsed_time(end_time) / 1000.0
        else:
            import time
            start = time.time()
            predictions = model(tensor)
            inference_time = time.time() - start
    
    # Postprocess predictions
    boxes, scores, labels = postprocess_predictions(
        predictions, original_shape, scale, confidence_threshold
    )
    
    print(f"⏱️  Inference time: {inference_time:.4f}s")
    print(f"🎯 Detected {len(boxes)} objects")
    
    # Print detection details
    for i, (box, score, label) in enumerate(zip(boxes, scores, labels)):
        class_name = class_names[label] if label < len(class_names) else f"Class_{label}"
        print(f"  {i+1}. {class_name}: {score:.3f} at [{box[0]:.0f}, {box[1]:.0f}, {box[2]:.0f}, {box[3]:.0f}]")
    
    return boxes, scores, labels, inference_time

def main():
    parser = argparse.ArgumentParser(description="YOLO-NAS Inference Demo")
    parser.add_argument("--checkpoint", required=True, help="Path to trained model checkpoint")
    parser.add_argument("--image", required=True, help="Path to input image")
    parser.add_argument("--architecture", default="yolo_nas_s", help="Model architecture")
    parser.add_argument("--num-classes", type=int, required=True, help="Number of classes")
    parser.add_argument("--class-names", nargs="+", required=True, help="List of class names")
    parser.add_argument("--input-size", nargs=2, type=int, default=[640, 640], help="Input image size")
    parser.add_argument("--confidence", type=float, default=0.5, help="Confidence threshold")
    parser.add_argument("--output", help="Output path for visualization")
    parser.add_argument("--no-display", action="store_true", help="Don't display visualization")
    
    args = parser.parse_args()
    
    print("🚀 YOLO-NAS Inference Demo")
    print("=" * 40)
    
    # Validate inputs
    if not Path(args.checkpoint).exists():
        print(f"❌ Checkpoint not found: {args.checkpoint}")
        sys.exit(1)
    
    if not Path(args.image).exists():
        print(f"❌ Image not found: {args.image}")
        sys.exit(1)
    
    # Load model
    model = load_model(args.checkpoint, args.architecture, args.num_classes, args.class_names)
    if model is None:
        sys.exit(1)
    
    # Run inference
    boxes, scores, labels, inference_time = run_inference(
        model, args.image, args.class_names, 
        tuple(args.input_size), args.confidence
    )
    
    # Visualize results
    if not args.no_display:
        output_path = args.output or f"inference_result_{Path(args.image).stem}.png"
        visualize_predictions(args.image, boxes, scores, labels, args.class_names, output_path)
    
    print("\n✅ Inference completed successfully!")
    print(f"📊 Performance: {1/inference_time:.1f} FPS")

if __name__ == "__main__":
    main()
