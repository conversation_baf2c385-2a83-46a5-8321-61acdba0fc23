# YOLO-NAS Training Guide for Apple Silicon

## Complete Step-by-Step Implementation for Mac Mini M4

This guide provides a comprehensive implementation for training YOLO-NAS object detection models on Apple Silicon hardware, specifically optimized for Mac Mini M4 with MPS (Metal Performance Shaders) backend.

## 🚀 Quick Start

### Prerequisites
- Mac Mini M4 (or other Apple Silicon Mac)
- macOS 13.0 or later
- UV package manager installed
- Python 3.9+ 
- At least 16GB RAM recommended
- Custom dataset in YOLO format

### 1. Environment Setup

First, run the environment setup script:

```bash
python setup_environment.py
```

This will:
- ✅ Check system requirements
- ✅ Verify UV installation  
- ✅ Create virtual environment
- ✅ Install dependencies with Apple Silicon optimizations
- ✅ Verify MPS availability
- ✅ Create project structure

### 2. Activate Environment

```bash
source yolo-nas-training/bin/activate
```

### 3. Install SuperGradients

```bash
uv pip install -e .
```

## 📁 Dataset Preparation

### Dataset Structure
Your dataset should follow this structure:

```
datasets/custom/your_dataset/
├── images/
│   ├── train/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   ├── val/
│   │   ├── image1.jpg
│   │   └── ...
│   └── test/ (optional)
├── labels/
│   ├── train/
│   │   ├── image1.txt
│   │   ├── image2.txt
│   │   └── ...
│   ├── val/
│   │   ├── image1.txt
│   │   └── ...
│   └── test/ (optional)
```

### Label Format
Each `.txt` file should contain YOLO format annotations:
```
class_id center_x center_y width height
```
Where all coordinates are normalized (0-1).

### 4. Setup Dataset Configuration

```bash
python scripts/setup_custom_dataset.py \
    --dataset-name "my_dataset" \
    --dataset-path "datasets/custom/my_dataset" \
    --classes "class1" "class2" "class3" \
    --input-size 640 640 \
    --batch-size 16 \
    --validate
```

This will:
- ✅ Validate dataset structure
- ✅ Check annotation format
- ✅ Generate dataset statistics
- ✅ Create dataset configuration file

## 🔧 Training Configuration

### 5. Create Training Configuration

```bash
python scripts/create_training_config.py \
    --dataset-name "my_dataset" \
    --num-classes 3 \
    --model-size "s" \
    --max-epochs 100 \
    --batch-size 16 \
    --initial-lr 5e-4
```

Available model sizes:
- `s`: YOLO-NAS-S (fastest, smallest)
- `m`: YOLO-NAS-M (balanced)
- `l`: YOLO-NAS-L (most accurate, largest)

### Apple Silicon Optimizations Applied:
- ✅ MPS backend configuration
- ✅ Optimized batch sizes for M4 memory
- ✅ Mixed precision training
- ✅ Efficient data loading (reduced workers)
- ✅ Memory-efficient augmentations

## 🏃 Training Execution

### 6. Start Training

```bash
python scripts/train_yolo_nas.py \
    --config "configs/custom/my_dataset_yolo_nas_s_apple_silicon.yaml" \
    --log-dir "logs"
```

### Resume Training (if interrupted)

```bash
python scripts/train_yolo_nas.py \
    --config "configs/custom/my_dataset_yolo_nas_s_apple_silicon.yaml" \
    --resume "checkpoints/custom/my_dataset/ckpt_best.pth"
```

### Monitor Training

```bash
tensorboard --logdir logs
```

Open http://localhost:6006 in your browser to view:
- Loss curves
- Learning rate schedule
- Validation metrics
- Training images with predictions

## 📊 Model Evaluation

### 7. Evaluate Trained Model

```bash
python scripts/evaluate_and_export.py \
    --checkpoint "checkpoints/custom/my_dataset/ckpt_best.pth" \
    --architecture "yolo_nas_s" \
    --num-classes 3 \
    --class-names "class1" "class2" "class3" \
    --input-size 640 640 \
    --export-dir "exports/my_dataset"
```

This will:
- ✅ Load trained model
- ✅ Benchmark inference performance
- ✅ Export to ONNX format
- ✅ Export to CoreML format (Apple optimized)
- ✅ Generate evaluation report

## 🚀 Model Export & Deployment

### Export Formats Generated:

1. **ONNX** (`model.onnx`): Cross-platform deployment
2. **CoreML** (`model.mlmodel`): Apple ecosystem optimization
3. **PyTorch** (`model.pth`): Native PyTorch format

### Performance Benchmarks:
The evaluation script provides:
- Mean inference time
- FPS (Frames Per Second)
- Memory usage
- Model accuracy metrics (mAP@0.5, mAP@0.5:0.95)

## 🔧 Configuration Customization

### Batch Size Recommendations for Mac Mini M4:
- **8GB RAM**: batch_size = 8-12
- **16GB RAM**: batch_size = 16-24  
- **24GB RAM**: batch_size = 24-32

### Learning Rate Guidelines:
- **Small datasets** (<1000 images): 1e-4 to 5e-4
- **Medium datasets** (1000-10000 images): 5e-4 to 1e-3
- **Large datasets** (>10000 images): 1e-3 to 2e-3

### Epoch Recommendations:
- **Small datasets**: 150-300 epochs
- **Medium datasets**: 100-200 epochs
- **Large datasets**: 50-100 epochs

## 🐛 Troubleshooting

### Common Issues:

1. **MPS not available**
   ```bash
   # Check PyTorch MPS support
   python -c "import torch; print(torch.backends.mps.is_available())"
   ```

2. **Out of memory errors**
   - Reduce batch size
   - Reduce input image size
   - Disable mixed precision

3. **Slow training**
   - Ensure MPS is being used
   - Check system activity monitor
   - Reduce number of data loading workers

4. **Poor convergence**
   - Adjust learning rate
   - Check data augmentation settings
   - Verify dataset quality

### Performance Optimization Tips:

1. **Memory Management**:
   ```bash
   # Clear MPS cache periodically
   python -c "import torch; torch.backends.mps.empty_cache()"
   ```

2. **Optimal Settings for M4**:
   - Use batch sizes that are multiples of 8
   - Enable mixed precision training
   - Use 4-6 data loading workers max

## 📈 Expected Performance

### YOLO-NAS-S on Mac Mini M4:
- **Training Speed**: ~2-3 seconds per epoch (small dataset)
- **Inference Speed**: ~15-25 FPS (640x640 input)
- **Memory Usage**: ~4-6GB during training

### Model Accuracy (COCO-pretrained):
- **YOLO-NAS-S**: mAP@0.5 ≈ 47.5%
- **YOLO-NAS-M**: mAP@0.5 ≈ 51.5%
- **YOLO-NAS-L**: mAP@0.5 ≈ 52.2%

## 📚 Additional Resources

- [YOLO-NAS Paper](https://arxiv.org/abs/2305.15376)
- [SuperGradients Documentation](https://docs.deci.ai/super-gradients/)
- [Apple Metal Performance Shaders](https://developer.apple.com/metal/)

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review logs in the `logs/` directory
3. Ensure all dependencies are correctly installed
4. Verify dataset format and structure

## 📝 Next Steps

After successful training:
1. **Fine-tune hyperparameters** based on results
2. **Implement data augmentation** strategies
3. **Deploy model** using exported formats
4. **Set up inference pipeline** for production use

---

**Happy Training! 🚀**
