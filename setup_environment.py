#!/usr/bin/env python3
"""
YOLO-NAS Training Environment Setup for Apple Silicon
Optimized for Mac Mini M4 with MPS backend
"""

import subprocess
import sys
import os
import platform
import torch
from pathlib import Path

def check_system_requirements():
    """Check if running on Apple Silicon Mac"""
    print("🔍 Checking system requirements...")
    
    # Check if running on macOS
    if platform.system() != "Darwin":
        raise RuntimeError("This setup is optimized for macOS with Apple Silicon")
    
    # Check if Apple Silicon
    try:
        result = subprocess.run(['uname', '-m'], capture_output=True, text=True)
        if 'arm64' not in result.stdout:
            print("⚠️  Warning: Not detected as Apple Silicon, but continuing...")
    except:
        print("⚠️  Could not detect architecture")
    
    print("✅ System requirements check passed")

def check_uv_installation():
    """Check if UV is installed"""
    print("🔍 Checking UV installation...")
    
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
        print(f"✅ UV found: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("❌ UV not found. Please install UV first:")
        print("curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False

def create_uv_environment():
    """Create UV virtual environment"""
    print("🔧 Creating UV virtual environment...")
    
    env_name = "yolo-nas-training"
    
    # Create virtual environment
    try:
        subprocess.run(['uv', 'venv', env_name], check=True)
        print(f"✅ Virtual environment '{env_name}' created")
        
        # Get the activation script path
        if platform.system() == "Windows":
            activate_script = f"{env_name}/Scripts/activate"
        else:
            activate_script = f"{env_name}/bin/activate"
        
        print(f"📝 To activate the environment, run:")
        print(f"   source {activate_script}")
        
        return env_name
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return None

def install_dependencies(env_name):
    """Install dependencies using UV"""
    print("📦 Installing dependencies with UV...")
    
    # Base dependencies for Apple Silicon
    dependencies = [
        # PyTorch with MPS support
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "torchaudio>=2.0.0",
        
        # Core dependencies from requirements.txt
        "tqdm>=4.57.0",
        "scipy>=1.6.1",
        "matplotlib>=3.3.4",
        "psutil>=5.8.0",
        "tensorboard>=2.4.1",
        "setuptools>=65.5.1,<67.0.0",
        "torchmetrics==0.8",
        "hydra-core>=1.2.0",
        "onnxruntime>=1.15.0",
        "onnx==1.15.0",
        "pillow>=10.2.0",
        "einops==0.3.2",
        "treelib==1.6.1",
        "termcolor==1.1.0",
        "packaging>=20.4",
        "stringcase>=1.2.0",
        "rapidfuzz",
        "json-tricks==3.16.1",
        "onnxsim>=0.4.3,<1.0",
        "albumentations~=1.3",
        "imagesize~=1.4.1",
        
        # Additional useful packages
        "jupyter",
        "ipywidgets",
        "opencv-python",
        "seaborn",
        "wandb",  # For experiment tracking
    ]
    
    try:
        # Install dependencies one by one to handle potential conflicts
        for dep in dependencies:
            print(f"Installing {dep}...")
            subprocess.run(['uv', 'pip', 'install', dep], check=True)
        
        print("✅ All dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def verify_mps_availability():
    """Verify MPS (Metal Performance Shaders) availability"""
    print("🔍 Checking MPS availability...")
    
    try:
        if torch.backends.mps.is_available():
            print("✅ MPS is available")
            
            # Test MPS functionality
            device = torch.device("mps")
            x = torch.randn(10, 10).to(device)
            y = torch.randn(10, 10).to(device)
            z = torch.mm(x, y)
            print("✅ MPS functionality test passed")
            
            return True
        else:
            print("❌ MPS is not available")
            return False
    except Exception as e:
        print(f"❌ Error checking MPS: {e}")
        return False

def create_project_structure():
    """Create project directory structure"""
    print("📁 Creating project structure...")
    
    directories = [
        "datasets",
        "datasets/custom",
        "datasets/custom/images",
        "datasets/custom/images/train",
        "datasets/custom/images/val",
        "datasets/custom/images/test",
        "datasets/custom/labels",
        "datasets/custom/labels/train",
        "datasets/custom/labels/val",
        "datasets/custom/labels/test",
        "configs",
        "configs/custom",
        "checkpoints",
        "checkpoints/custom",
        "logs",
        "exports",
        "scripts",
        "notebooks",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Project structure created")

def main():
    """Main setup function"""
    print("🚀 YOLO-NAS Training Environment Setup for Apple Silicon")
    print("=" * 60)
    
    # Check system requirements
    check_system_requirements()
    
    # Check UV installation
    if not check_uv_installation():
        sys.exit(1)
    
    # Create virtual environment
    env_name = create_uv_environment()
    if not env_name:
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies(env_name):
        sys.exit(1)
    
    # Verify MPS
    verify_mps_availability()
    
    # Create project structure
    create_project_structure()
    
    print("\n🎉 Environment setup completed successfully!")
    print("\n📋 Next steps:")
    print(f"1. Activate the environment: source {env_name}/bin/activate")
    print("2. Install super-gradients in development mode: uv pip install -e .")
    print("3. Prepare your custom dataset in YOLO format")
    print("4. Run the training configuration script")

if __name__ == "__main__":
    main()
