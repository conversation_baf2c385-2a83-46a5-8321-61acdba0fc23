#!/usr/bin/env python3
"""
YOLO-NAS Training Environment Setup for Apple Silicon
Optimized for Mac Mini M4 with MPS backend
"""

import subprocess
import sys
import os
import platform
import torch
from pathlib import Path

def check_system_requirements():
    """Check if running on Apple Silicon Mac"""
    print("🔍 Checking system requirements...")
    
    # Check if running on macOS
    if platform.system() != "Darwin":
        raise RuntimeError("This setup is optimized for macOS with Apple Silicon")
    
    # Check if Apple Silicon
    try:
        result = subprocess.run(['uname', '-m'], capture_output=True, text=True)
        if 'arm64' not in result.stdout:
            print("⚠️  Warning: Not detected as Apple Silicon, but continuing...")
    except:
        print("⚠️  Could not detect architecture")
    
    print("✅ System requirements check passed")

def check_uv_installation():
    """Check if UV is installed"""
    print("🔍 Checking UV installation...")
    
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
        print(f"✅ UV found: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("❌ UV not found. Please install UV first:")
        print("curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False

def install_dependencies():
    """Install dependencies using UV (no separate venv needed)"""
    print("📦 Installing dependencies with UV...")

    # Base dependencies for Apple Silicon
    dependencies = [
        # PyTorch with MPS support
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "torchaudio>=2.0.0",

        # Core dependencies from requirements.txt
        "tqdm>=4.57.0",
        "scipy>=1.6.1",
        "matplotlib>=3.3.4",
        "psutil>=5.8.0",
        "tensorboard>=2.4.1",
        "setuptools>=65.5.1,<67.0.0",
        "torchmetrics==0.8",
        "hydra-core>=1.2.0",
        "onnxruntime>=1.15.0",
        "onnx==1.15.0",
        "pillow>=10.2.0",
        "einops==0.3.2",
        "treelib==1.6.1",
        "termcolor==1.1.0",
        "packaging>=20.4",
        "stringcase>=1.2.0",
        "rapidfuzz",
        "json-tricks==3.16.1",
        "onnxsim>=0.4.3,<1.0",
        "albumentations~=1.3",
        "imagesize~=1.4.1",

        # Additional useful packages
        "jupyter",
        "ipywidgets",
        "opencv-python",
        "seaborn",
        "wandb",  # For experiment tracking
    ]

    try:
        # Install all dependencies with UV
        print("Installing PyTorch with MPS support...")
        subprocess.run(['uv', 'add'] + dependencies[:3], check=True)

        print("Installing core dependencies...")
        subprocess.run(['uv', 'add'] + dependencies[3:], check=True)

        print("✅ All dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def verify_mps_availability():
    """Verify MPS (Metal Performance Shaders) availability"""
    print("🔍 Checking MPS availability...")
    
    try:
        if torch.backends.mps.is_available():
            print("✅ MPS is available")
            
            # Test MPS functionality
            device = torch.device("mps")
            x = torch.randn(10, 10).to(device)
            y = torch.randn(10, 10).to(device)
            z = torch.mm(x, y)
            print("✅ MPS functionality test passed")
            
            return True
        else:
            print("❌ MPS is not available")
            return False
    except Exception as e:
        print(f"❌ Error checking MPS: {e}")
        return False

def create_project_structure():
    """Create project directory structure"""
    print("📁 Creating project structure...")
    
    directories = [
        "datasets",
        "datasets/custom",
        "datasets/custom/images",
        "datasets/custom/images/train",
        "datasets/custom/images/val",
        "datasets/custom/images/test",
        "datasets/custom/labels",
        "datasets/custom/labels/train",
        "datasets/custom/labels/val",
        "datasets/custom/labels/test",
        "configs",
        "configs/custom",
        "checkpoints",
        "checkpoints/custom",
        "logs",
        "exports",
        "scripts",
        "notebooks",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Project structure created")

def main():
    """Main setup function"""
    print("🚀 YOLO-NAS Training Environment Setup for Apple Silicon")
    print("=" * 60)

    # Check system requirements
    check_system_requirements()

    # Check UV installation
    if not check_uv_installation():
        sys.exit(1)

    # Initialize UV project if pyproject.toml doesn't exist
    if not Path("pyproject.toml").exists():
        print("🔧 Initializing UV project...")
        try:
            subprocess.run(['uv', 'init', '--no-readme'], check=True)
            print("✅ UV project initialized")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to initialize UV project: {e}")
            sys.exit(1)

    # Install dependencies
    if not install_dependencies():
        sys.exit(1)

    # Verify MPS
    verify_mps_availability()

    # Create project structure
    create_project_structure()

    print("\n🎉 Environment setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Install super-gradients in development mode: uv add -e .")
    print("2. Prepare your custom dataset in YOLO format")
    print("3. Run the training configuration script")
    print("4. All commands will use UV automatically (no activation needed)")

if __name__ == "__main__":
    main()
