## Super-Gradients configuration for black/mypy/isort etc tools.
[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 160

## Configuration for Black.
[tool.black]
line-length = 160
target-version = ['py36', 'py37', 'py38', 'py39' ]
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.circleci
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''
