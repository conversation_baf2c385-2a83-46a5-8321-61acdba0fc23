<div align="center">
  <img src="./images/SG - Horizontal Glow 2.png" width="600"/>
 <br/><br/>
  

</div>


# SuperGradients

## Introduction
Welcome to SuperGradients, a free, open-source training library for PyTorch-based deep learning models.
SuperGradients allows you to train or fine-tune SOTA pre-trained models for all the most commonly applied computer vision tasks with just one training library. We currently support object detection, image classification and semantic segmentation for videos and images.


## Why use SuperGradients?
 
### Built-in SOTA Models

Easily load and fine-tune production-ready, [pre-trained SOTA models](model_zoo.md) that incorporate best practices and validated hyper-parameters for achieving best-in-class accuracy (Yolox, PP-YoloE, STDC, DDRNet, and PP-LiteSeg).

### Easily Reproduce our Results
       
Why do all the grind work, if we already did it for you? leverage tested and proven [recipes](https://github.com/Deci-AI/super-gradients/tree/master/src/super_gradients/recipes) & [code examples](https://github.com/Deci-AI/super-gradients/tree/master/src/super_gradients/examples) for a wide range of computer vision models generated by our team of deep learning experts. Easily configure your own or use plug & play hyperparameters for training, dataset, and architecture.
    
### Production Readiness and Ease of Integration
    
All SuperGradients models’ are production ready in the sense that they are compatible with deployment tools such as TensorRT (Nvidia) and OpenVINO (Intel) and can be easily taken into production. With a few lines of code you can easily integrate the models into your codebase.
   
## Getting Started
Check out our [Quickstart tutorial](QuickstartBasicToolkit.md) to get learn the basic of SuperGradients.

You can also start from our tutorial on [Detection](ObjectDetection.md), [Segmentation](Segmentation.md) or [Pose Estimation](PoseEstimation.md).
 
## What's New

__________________________________________________________________________________________________________
Version 3.6.1 (March 6, 2024)

* A dependency from `pycocotools` has been removed from SG, we don't rely anymore on this package to parse COCO dataset json. 
* A `Trainer.ptq` and `Trainer.qat` methods now allow granular control on for the model should be exported (with or without pre-/post-processing).
* A `model.predict` now has `fp16` argument (Default is `True`) which one can use to disable mixed precision feature (Addressing issues on GTX 16XX series)
* Fixed a bug in missing min-max image normalization in `plot()` method for detection dataset.
* Removed `deci-common` from `[pro]` requirements.
* Updated [YoloNAS-Pose fine-tunining for Animals Pose Dataset](https://github.com/Deci-AI/super-gradients/blob/master/notebooks/YoloNAS_Pose_Fine_Tuning_Animals_Pose_Dataset.ipynb) notebook.
* 
__________________________________________________________________________________________________________
Version 3.6.0 (Jan 25, 2024)

* Added segmentation samples and support for albumentation transforms for segmentation
* Implemented distance-based detection matching in `DetectionMetrics` as an enhancement (by @DimaBir)
* New training hyperparameter - finetune, and multiple LR assignment read about it [https://github.com/Deci-AI/super-gradients/blob/master/documentation/source/LRAssignment.md](here)
* Enhanced `ImagePermute` processing inclusion
* Improved dataset plotting and plot functionality
* A new API for checking model input compatibility
* Extended `predict()` support for segmentation models

Version 3.5.0 (November 23, 2023)

* Support for long videos in `model.predict()` (by @hakuryuu96)
* Added support for multiple test loaders in `train_from_config`
* Added skip_resize to `model.predict()` to support large images and small objects
  
Version 3.4.0 (November 6, 2023)

* [YoloNAS-Pose](YOLONAS-POSE.md) model released - a new frontier in pose estimation
* Added option to export a recipe to a single YAML file or to a standalone train.py file 
* Other bugfixes & minor improvements. Full release notes available [here](https://github.com/Deci-AI/super-gradients/releases/tag/3.4.0)

## Citation

If you are using SuperGradients library in your research, please cite SuperGradients deep learning training library.


[//]: # (### BibTeX)

[//]: # ()
[//]: # (```bibtex)

[//]: # (@misc{rw2019timm,)

[//]: # (  title = {SuperGradients},)

[//]: # (  year = {2021},)

[//]: # (  publisher = {GitHub},)

[//]: # (  journal = {GitHub repository},)

[//]: # (  doi = {},)

[//]: # (  howpublished = {\url{https://github.com/Deci-AI/super-gradients}})

[//]: # (})

[//]: # (```)

[//]: # ()
[//]: # (### Latest DOI)

[//]: # ()
[//]: # ([![DOI]&#40;https://zenodo.org/badge/.svg&#41;]&#40;https://zenodo.org/badge/latestdoi/&#41;)

## Community

If you want to be a part of SuperGradients growing community, hear about all the exciting news and updates, need help, request for advanced features,
    or want to file a bug or issue report, we would love to welcome you aboard!

* Slack is the place to be and ask questions about SuperGradients and get support. [Click here to join our Slack](
  https://join.slack.com/t/supergradients-comm52/shared_invite/zt-10vz6o1ia-b_0W5jEPEnuHXm087K~t8Q)
    
* To report a bug, [file an issue](https://github.com/Deci-AI/super-gradients/issues) on GitHub.

* Join the [SG Newsletter](https://www.supergradients.com/#Newsletter)
  for staying up to date with new features and models, important announcements, and upcoming events.

## License

This project is released under the [Apache 2.0 license](LICENSE).
    
## Citing

### BibTeX

```bibtex
@misc{supergradients,
  doi = {10.5281/ZENODO.7789328},
  url = {https://zenodo.org/record/7789328},
  author = {Aharon,  Shay and {Louis-Dupont} and {Ofri Masad} and Yurkova,  Kate and {Lotem Fridman} and {Lkdci} and Khvedchenya,  Eugene and Rubin,  Ran and Bagrov,  Natan and Tymchenko,  Borys and Keren,  Tomer and Zhilko,  Alexander and {Eran-Deci}},
  title = {Super-Gradients},
  publisher = {GitHub},
  journal = {GitHub repository},
  year = {2021},
}
```

### Latest DOI

[![DOI](https://zenodo.org/badge/DOI/10.5281/zenodo.7789328.svg)](https://doi.org/10.5281/zenodo.7789328)
