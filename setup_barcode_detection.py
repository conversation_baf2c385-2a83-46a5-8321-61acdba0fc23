#!/usr/bin/env python3
"""
Complete setup script for YOLO-NAS barcode detection on Apple Silicon
This script handles the entire pipeline from COCO conversion to training setup
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from datetime import datetime

def run_command(command: str, description: str):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check if COCO dataset exists
    coco_dataset = Path("Barcodes.v5i.coco")
    if not coco_dataset.exists():
        print(f"❌ COCO dataset not found: {coco_dataset}")
        return False
    
    # Check if UV is available
    try:
        subprocess.run(['uv', '--version'], capture_output=True, check=True)
        print("✅ UV package manager found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ UV package manager not found")
        print("Please install UV: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False
    
    # Check if UV project is initialized
    pyproject_path = Path("pyproject.toml")
    if not pyproject_path.exists():
        print("⚠️  UV project not initialized, will create it")
    else:
        print("✅ UV project found")
    
    print("✅ Prerequisites check passed")
    return True

def setup_environment():
    """Setup the training environment"""
    print("\n🔧 Setting up environment...")
    
    # Run environment setup
    if not run_command("python setup_environment.py", "Environment setup"):
        return False
    
    # Install SuperGradients in development mode using UV
    install_cmd = "uv add -e ."

    if not run_command(install_cmd, "SuperGradients installation"):
        return False
    
    return True

def convert_dataset():
    """Convert COCO dataset to YOLO format"""
    print("\n📊 Converting dataset from COCO to YOLO format...")
    
    convert_cmd = "uv run python scripts/convert_coco_to_yolo.py --input-dir Barcodes.v5i.coco --output-dir datasets/barcodes_yolo"
    
    return run_command(convert_cmd, "Dataset conversion")

def setup_training_config(model_size: str, batch_size: int, max_epochs: int):
    """Setup training configuration"""
    print(f"\n⚙️  Setting up training configuration for YOLO-NAS-{model_size.upper()}...")
    
    config_cmd = f"uv run python scripts/setup_barcode_training.py --model-size {model_size} --batch-size {batch_size} --max-epochs {max_epochs}"
    
    return run_command(config_cmd, "Training configuration")

def validate_dataset():
    """Validate the converted dataset"""
    print("\n🔍 Validating converted dataset...")
    
    validate_cmd = "uv run python scripts/setup_custom_dataset.py --dataset-name barcodes --dataset-path datasets/barcodes_yolo --classes Barcode 'QR Code' --validate"
    
    return run_command(validate_cmd, "Dataset validation")

def create_quick_start_guide():
    """Create a quick start guide"""
    
    guide_content = f"""# YOLO-NAS Barcode Detection - Quick Start Guide

## Setup completed on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

### 🎯 Your Dataset
- **Type**: Barcode Detection (Linear Barcodes + QR Codes)
- **Training Images**: 28,696
- **Validation Images**: 2,382  
- **Test Images**: 432
- **Classes**: 2 (Barcode, QR Code)
- **Format**: Converted from COCO to YOLO

### 🚀 Start Training

1. **Start Training** (choose one):
   ```bash
   # YOLO-NAS-S (fastest, recommended for Mac Mini M4)
   ./train_barcode_s.sh

   # YOLO-NAS-M (balanced)
   ./train_barcode_m.sh

   # YOLO-NAS-L (most accurate, requires more memory)
   ./train_barcode_l.sh
   ```

2. **Monitor Training**:
   ```bash
   uv run tensorboard --logdir logs
   ```
   Open http://localhost:6006 in your browser

   **Note**: UV automatically manages the environment - no activation needed!

### 📊 Expected Results
- **Training Time**: 2-3 hours on Mac Mini M4
- **Expected mAP@0.5**: 85-95% (excellent for barcodes)
- **Inference Speed**: 20-30 FPS on Apple Silicon
- **Memory Usage**: 4-6GB during training

### 🔧 Configuration Files
- Dataset config: `configs/custom/barcodes_dataset_params.yaml`
- Training config: `configs/custom/barcodes_yolo_nas_s_training.yaml`
- Checkpoints: `checkpoints/custom/barcodes/`

### 🎯 After Training
1. **Evaluate Model**:
   ```bash
   uv run python scripts/evaluate_and_export.py \\
       --checkpoint checkpoints/custom/barcodes/ckpt_best.pth \\
       --architecture yolo_nas_s \\
       --num-classes 2 \\
       --class-names "Barcode" "QR Code"
   ```

2. **Test Inference**:
   ```bash
   uv run python scripts/inference_demo.py \\
       --checkpoint checkpoints/custom/barcodes/ckpt_best.pth \\
       --image path/to/test/image.jpg \\
       --architecture yolo_nas_s \\
       --num-classes 2 \\
       --class-names "Barcode" "QR Code"
   ```

### 🐛 Troubleshooting
- **Out of memory**: Reduce batch size in training config
- **Slow training**: Check MPS is being used with `python -c "import torch; print(torch.backends.mps.is_available())"`
- **Poor results**: Try increasing epochs or adjusting learning rate

### 📚 Documentation
- Complete guide: `YOLO_NAS_TRAINING_GUIDE.md`
- Apple Silicon setup: `README_APPLE_SILICON.md`

**Happy training! 🚀**
"""
    
    with open("QUICK_START_BARCODE.md", 'w') as f:
        f.write(guide_content)
    
    print("✅ Quick start guide created: QUICK_START_BARCODE.md")

def main():
    parser = argparse.ArgumentParser(description="Complete YOLO-NAS barcode detection setup")
    parser.add_argument("--model-size", choices=["s", "m", "l"], default="s", help="Model size (s/m/l)")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size")
    parser.add_argument("--max-epochs", type=int, default=100, help="Maximum training epochs")
    parser.add_argument("--skip-env-setup", action="store_true", help="Skip environment setup")
    parser.add_argument("--skip-conversion", action="store_true", help="Skip dataset conversion")
    
    args = parser.parse_args()
    
    print("🍎 YOLO-NAS Barcode Detection Setup for Apple Silicon")
    print("=" * 70)
    print(f"🏗️  Model: YOLO-NAS-{args.model_size.upper()}")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"🏃 Max epochs: {args.max_epochs}")
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Setup environment
    if not args.skip_env_setup:
        if not setup_environment():
            print("❌ Environment setup failed")
            sys.exit(1)
    else:
        print("⏭️  Skipping environment setup")
    
    # Convert dataset
    if not args.skip_conversion:
        if not convert_dataset():
            print("❌ Dataset conversion failed")
            sys.exit(1)
    else:
        print("⏭️  Skipping dataset conversion")
    
    # Validate dataset
    if not validate_dataset():
        print("❌ Dataset validation failed")
        sys.exit(1)
    
    # Setup training configuration
    if not setup_training_config(args.model_size, args.batch_size, args.max_epochs):
        print("❌ Training configuration failed")
        sys.exit(1)
    
    # Create quick start guide
    create_quick_start_guide()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 What's ready:")
    print("  ✅ Environment with UV and SuperGradients")
    print("  ✅ Dataset converted from COCO to YOLO format")
    print("  ✅ Training configuration optimized for Apple Silicon")
    print("  ✅ Training scripts generated")
    print("  ✅ Quick start guide created")
    
    print(f"\n🚀 Ready to train! Run:")
    print(f"  ./train_barcode_{args.model_size}.sh")
    print(f"  (UV automatically manages dependencies - no activation needed!)")
    
    print(f"\n📖 See QUICK_START_BARCODE.md for detailed instructions")

if __name__ == "__main__":
    main()
