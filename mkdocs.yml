site_name: super-gradients
docs_dir: .
nav:
- Welcome:
  - Intro: ./documentation/source/welcome.md
  - Installation: ./documentation/source/installation.md
  - Model Zoo: ./documentation/source/model_zoo.md
- Quick Start:
  - Basic: ./documentation/source/QuickstartBasicToolkit.md
  - Classification: ./documentation/source/Example_Classification.md
  - Object Detection: ./documentation/source/ObjectDetection.md
  - Segmentation: ./documentation/source/Segmentation.md
  - Pose Estimation: ./documentation/source/PoseEstimation.md
  - Training an external model: ./documentation/source/Example_Training-an-external-model.md
  - Pretrained Model Prediction:
    - Prediction: ./documentation/source/ModelPredictions.md
    - Custom training Setup: ./documentation/source/PredictionSetup.md
- Main Components:
  - Models: ./documentation/source/models.md
  - Dataset:
    - Data: ./documentation/source/Data.md
    - Computer Vision Datasets: ./src/super_gradients/training/datasets/Dataset_Setup_Instructions.md
    - Dataset Adapter: ./documentation/source/dataloader_adapter.md
  - Loss functions: ./documentation/source/Losses.md
  - LR Assignment: ./documentation/source/LRAssignment.md
  - LR schedulers: ./documentation/source/LRScheduling.md
  - Metrics: ./documentation/source/Metrics.md
  - Optimizers: ./documentation/source/optimizers.md
  - Phase Callbacks: ./documentation/source/PhaseCallbacks.md
  - YAMLs and Recipes:
    - Configurations: ./documentation/source/configuration_files.md
    - Training: ./documentation/source/Recipes_Training.md
    - Factories: ./documentation/source/Recipes_Factories.md
    - Custom Recipes: ./documentation/source/Recipes_Custom.md
  - Experiment Management: ./documentation/source/experiment_management.md
  - Checkpoints: ./documentation/source/Checkpoints.md
  - Docker: ./documentation/source/SGDocker.md
  - Output Adapter: ./documentation/source/DetectionOutputAdapter.md
- Features:
  - Training Modes: ./documentation/source/device.md
  - Logging: ./documentation/source/logs.md
  - Experiment Monitoring: ./documentation/source/experiment_monitoring.md
  - Exponential Moving Average (EMA): ./documentation/source/EMA.md
  - Automatic Mixed Precision (AMP): ./documentation/source/average_mixed_precision.md
  - Knowledge Distillation (KD): ./documentation/source/KD.md
  - Quantization (PTQ & QAT): ./documentation/source/ptq_qat.md
  - Model Export (ONNX & TensorRT):
    - Object Detection: ./documentation/source/models_export.md
    - Pose Estimation: ./documentation/source/models_export_pose.md
- Troubleshooting: ./documentation/source/troubleshooting.md
- Contribution: ./documentation/source/CONTRIBUTING.md
- Code: this_is_automatically_generated.md
