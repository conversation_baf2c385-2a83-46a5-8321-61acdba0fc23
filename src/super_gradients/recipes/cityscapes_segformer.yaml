#  SegFormer segmentation training example with Cityscapes dataset.
#  Reproduction of paper:
#  <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,  <PERSON>, <PERSON>
#  "SegFormer: Simple and Efficient Design for Semantic Segmentation with Transformers"
#  ( https://arxiv.org/pdf/2105.15203.pdf )
#
#  Official git repo:
#      https://github.com/NVlabs/SegFormer
#
#  Code and Imagenet-1k pre-trained backbone weights taken and adapted from:
#      https://github.com/sithu31296/semantic-segmentation
#
# Instructions:
# In the recipe of the specif variant you would like to train:
#   1. Choose SegFormer architecture (b0 - b5) by changing the value of the "architecture".
#   2. We recommend preparing the data according to SG's CityScapes readme file:
#      https://github.com/Deci-AI/super-gradients/blob/master/src/super_gradients/training/datasets/Dataset_Setup_Instructions.md
#   3. Note: if you change the dataset's internal directory structure, make changes to the fields "list_file" and
#      "labels_csv_path" of both "train_dataset_params" and "val_dataset_params" accordingly
#   4. Edit the "data_root_dir" field to point to the absolute path of the data root directory
#   5. Edit the "ckpt_root_dir" field to the path where you want to save checkpoints and logs
#   6. Move to the project root (where you will find the ReadMe and src folder)
#   7. Run the command (change the config_name according to the variant):
#       python -m super_gradients.train_from_recipe --config-name=cityscapes_segformer_b0
#
#
# Imagenet-1K pre-trained backbone:
#   MiT (Mix Transformer) B0:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b0.pth
#                         B1:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b1.pth
#                         B2:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b2.pth
#                         B3:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b3.pth
#                         B4:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b4.pth
#                         B5:   https://deci-pretrained-models.s3.amazonaws.com/mit_backbones/mit_b5.pth
#
#   8. Download the weights from the above link and put them in a directory of your choice
#   9. Insert the weights file's full path to checkpoint_params.checkpoint_path:
#   10. Ensure checkpoint_params.load_backbone: True
#
#
# Performance and training details:
#      SegFormer-B0: mIoU (sliding-window inference) on validation set: 76.14
#                    training time: 6 hours with 8 NVIDIA RTX A5000 GPUs with DDP, ~1 minutes / epoch
#      SegFormer-B1: mIoU (sliding-window inference) on validation set: 77.80
#                    training time: 8 hours with 8 NVIDIA RTX A5000 GPUs with DDP, ~1 minutes / epoch
#      SegFormer-B2: mIoU (sliding-window inference) on validation set: 81.43
#                    training time: 26 hours with 4 NVIDIA RTX A5000 GPUs with DDP, ~4 minutes / epoch
#      SegFormer-B3: mIoU (sliding-window inference) on validation set: 82.24
#                    training time: 36 hours with 4 NVIDIA RTX A5000 GPUs with DDP, ~3 minutes / epoch
#      SegFormer-B4: mIoU (sliding-window inference) on validation set: 82.39
#                    training time: 25 hours with 4 NVIDIA RTX A5000 GPUs with DDP, ~3 minutes / epoch
#      SegFormer-B5: mIoU (sliding-window inference) on validation set: 82.33
#                    training time: 41 hours with 4 NVIDIA RTX A5000 GPUs with DDP, ~3 minutes / epoch


defaults:
  - training_hyperparams: default_train_params
  - dataset_params: cityscapes_segformer_dataset_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

data_root_dir: /data/cityscapes
dataset_params:
  train_dataset_params:
    root_dir: ${data_root_dir}
  val_dataset_params:
    root_dir: ${data_root_dir}

experiment_name: ${architecture}_cityscapes


train_dataloader: cityscapes_train
val_dataloader: cityscapes_val

cityscapes_ignored_label: 19    # convenience parameter since it is used in many places in the YAML

arch_params:
  num_classes: 19

checkpoint_params:
  checkpoint_path:
  load_backbone: True
  load_weights_only: True
  strict_load: no_key_matching

load_checkpoint: False

resume: False
training_hyperparams:

  resume: ${resume}

  optimizer: AdamW
  zero_weight_decay_on_bias_and_bn: True

  sync_bn: True

  loss: CrossEntropyLoss
  criterion_params:
    ignore_index: ${cityscapes_ignored_label}

  phase_callbacks:
    - SlidingWindowValidationCallback:
        transforms_for_sliding_window: []

  train_metrics_list:
    - IoU:
        num_classes: 20
        ignore_index: ${cityscapes_ignored_label}

  valid_metrics_list:
    - IoU:
        num_classes: 20
        ignore_index: ${cityscapes_ignored_label}

  metric_to_watch: IoU
  greater_metric_to_watch_is_better: True
