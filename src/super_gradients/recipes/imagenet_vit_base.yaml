#  ViT Imagenet1K fine tuning from Imagenet21K classification training:
#  This example trains with batch_size = 64 * 8 GPUs, total 512.
#  Training time on 8 x GeForce RTX A5000 is 15min / epoch.
#  ViT base : 84.15
#
#  Log and tensorboard at s3://deci-pretrained-models/vit_base_imagenet1k/

# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       python -m super_gradients.train_from_recipe --config-name=imagenet_vit_base


defaults:
  - training_hyperparams: imagenet_vit_train_params
  - dataset_params: imagenet_vit_base_dataset_params
  - arch_params: vit_base_arch_params
  - checkpoint_params: vit_base_imagenet_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: imagenet_train
val_dataloader: imagenet_val



resume: False
training_hyperparams:
  resume: ${resume}

experiment_name: vit_base_imagenet1k

architecture: vit_base
multi_gpu: DDP
num_gpus: 8
