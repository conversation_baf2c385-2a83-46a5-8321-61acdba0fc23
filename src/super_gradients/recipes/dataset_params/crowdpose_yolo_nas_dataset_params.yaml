num_joints: 14

# OKs sigma values taken from
# https://github.com/Jeff-sjtu/CrowdPose/blob/master/crowdpose-api/PythonAPI/crowdposetools/cocoeval.py#L223
oks_sigmas: [0.079, 0.079, 0.072, 0.072, 0.062, 0.062, 0.107, 0.107, 0.087, 0.087, 0.089, 0.089, 0.079, 0.079]

flip_indexes: [ 1, 0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 12, 13]

edge_colors:
  - [214, 39, 40] # left_shoulder -> neck
  - [148, 103, 189] # right_shoulder -> neck
  - [44, 160, 44] # neck -> head
  - [188, 189, 34] # left_shoulder -> left_elbow
  - [31, 119, 180] # left_elbow -> left_wrist
  - [23, 190, 207] # right_shoulder -> right_elbow
  - [255, 127, 14]  # right_elbow -> right_wrist
  - [140, 86, 75] # left_shoulder -> left_hip
  - [227, 119, 194] # right_shoulder -> right_hip
  - [255, 127, 14] # left_hip -> left_knee
  - [31, 119, 180]  # left_knee -> left_ankle
  - [214, 39, 40] # right_hip -> right_knee
  - [44, 160, 44] # right_knee -> right_ankle
  - [148, 103, 189] # left_hip -> right_hip

edge_links:
  - [0, 13] # left_shoulder -> neck
  - [1, 13] # right_shoulder -> neck
  - [13, 12] # neck -> head
  - [0, 2] # left_shoulder -> left_elbow
  - [2, 4] # left_elbow -> left_wrist
  - [1, 3] # right_shoulder -> right_elbow
  - [3, 5] # right_elbow -> right_wrist
  - [0, 6] # left_shoulder -> left_hip
  - [1, 7] # right_shoulder -> right_hip
  - [6, 8] # left_hip -> left_knee
  - [8, 10]  # left_knee -> left_ankle
  - [7, 9] # right_hip -> right_knee
  - [9, 11] # right_knee -> right_ankle
  - [6, 7] # left_hip -> right_hip

keypoint_colors:
  - [148, 103, 189] # left_shoulder   (0)
  - [31, 119, 180]  # right_shoulder  (1)
  - [148, 103, 189] # left_elbow      (2)
  - [31, 119, 180]  # right_elbow     (3)
  - [148, 103, 189] # left_wrist      (4)
  - [31, 119, 180]  # right_wrist     (5)
  - [148, 103, 189] # left_hip        (6)
  - [31, 119, 180]  # right_hip       (7)
  - [148, 103, 189] # left_knee       (8)
  - [31, 119, 180]  # right_knee      (9)
  - [148, 103, 189] # left_ankle      (10)
  - [31, 119, 180]  # right_ankle     (11)
  - [148, 103, 189] # head            (12)
  - [31, 119, 180]  # neck            (13)

image_size: 640
dataset_params_suffix: "default_${dataset_params.image_size}"

train_dataset_params:
  data_dir: /data/crowdpose
  images_dir: images
  json_file: crowdpose_trainval.json
  include_empty_samples: True
  crowd_annotations_action: mask_as_normal

  edge_links: ${dataset_params.edge_links}
  edge_colors: ${dataset_params.edge_colors}
  keypoint_colors: ${dataset_params.keypoint_colors}

  transforms:
    - KeypointsRandomHorizontalFlip:
        flip_index: ${dataset_params.flip_indexes}
        prob: 0.5

    - KeypointsBrightnessContrast:
        brightness_range: [ 0.8, 1.2 ]
        contrast_range: [ 0.8, 1.2 ]
        prob: 0.5

    - KeypointsHSV:
        hgain: 20
        sgain: 20
        vgain: 20
        prob: 0.5

    - KeypointsRandomAffineTransform:
        max_rotation: 0
        min_scale: 0.66
        max_scale: 1.5
        max_translate: 0.1
        image_pad_value: 127
        mask_pad_value: 1
        prob: 0.75
        interpolation_mode: [0, 1, 2, 3, 4]

    - KeypointsLongestMaxSize:
        max_height: ${dataset_params.image_size}
        max_width: ${dataset_params.image_size}

    - KeypointsPadIfNeeded:
        min_height: ${dataset_params.image_size}
        min_width: ${dataset_params.image_size}
        image_pad_value: [127, 127, 127]
        mask_pad_value: 1
        padding_mode: center

    - KeypointsImageStandardize:
        max_value: 255

    - KeypointsRemoveSmallObjects:
        min_instance_area: 1
        min_visible_keypoints: 1

val_dataset_params:
  data_dir: /data/crowdpose
  images_dir: images
  json_file: crowdpose_test.json
  include_empty_samples: True
  crowd_annotations_action: no_action

  edge_links: ${dataset_params.edge_links}
  edge_colors: ${dataset_params.edge_colors}
  keypoint_colors: ${dataset_params.keypoint_colors}

  transforms:
    - KeypointsLongestMaxSize:
        max_height: ${dataset_params.image_size}
        max_width: ${dataset_params.image_size}

    - KeypointsPadIfNeeded:
        min_height: ${dataset_params.image_size}
        min_width: ${dataset_params.image_size}
        image_pad_value: 127
        mask_pad_value: 1
        padding_mode: bottom_right

    - KeypointsImageStandardize:
        max_value: 255

train_dataloader_params:
  dataset: COCOPoseEstimationDataset
  batch_size: 8
  num_workers: 8
  drop_last: True
  pin_memory: False
  shuffle: True
  collate_fn: YoloNASPoseCollateFN

val_dataloader_params:
  dataset: COCOPoseEstimationDataset
  batch_size: 24
  num_workers: 8
  drop_last: False
  shuffle: False
  pin_memory: False
  collate_fn: YoloNASPoseCollateFN
