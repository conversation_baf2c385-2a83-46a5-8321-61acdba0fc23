# Equivalent to cifar10_dataset_params.yaml, but uses albumentations transforms.
# The purpose of the below configuration is to demonstrate the use of Albumentation transforms in train_from_recipe.

batch_size: 256 # batch size for trainset
val_batch_size: 512 # batch size for valset in DatasetInterface

# TODO: REMOVE ABOVE, HERE FOR COMPATIBILITY UNTIL WE REMOVE DATASET_INTERFACE

train_dataset_params:
  root: ./data/cifar10
  train: True
  transforms:
    Albumentations:
      Compose:
        transforms:
          - RandomCrop:
              height: 32
              width: 32
          - HorizontalFlip:
              p: 0.5
          - Normalize:
              mean:
                - 0.4914
                - 0.4822
                - 0.4465
              std:
                - 0.2023
                - 0.1994
                - 0.2010
          - ToTensorV2
  target_transform: null
  download: True

train_dataloader_params:
  shuffle: True
  batch_size: 256
  num_workers: 8
  drop_last: False
  pin_memory: True

val_dataset_params:
  root: ./data/cifar10
  train: False
  transforms:
    Albumentations:
      Compose:
        transforms:
          - Normalize:
              mean:
                - 0.4914
                - 0.4822
                - 0.4465
              std:
                - 0.2023
                - 0.1994
                - 0.2010
          - ToTensorV2
  target_transform: null
  download: True

val_dataloader_params:
  batch_size: 512
  num_workers: 8
  drop_last: False
  pin_memory: True
