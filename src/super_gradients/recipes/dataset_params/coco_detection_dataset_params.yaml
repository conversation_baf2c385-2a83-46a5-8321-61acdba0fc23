train_dataset_params:
  data_dir: /data/coco # root path to coco data
  subdir: images/train2017 # sub directory path of data_dir containing the train data.
  json_file: instances_train2017.json # path to coco train json file, data_dir/annotations/train_json_file.
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
    - DetectionMosaic:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        prob: 1.
    - DetectionRandomAffine:
        degrees: 10.                  # rotation degrees, randomly sampled from [-degrees, degrees]
        translate: 0.1                # image translation fraction
        scales: [ 0.1, 2 ]              # random rescale range (keeps size by padding/cropping) after mosaic transform.
        shear: 2.0                    # shear degrees, randomly sampled from [-degrees, degrees]
        target_size: ${dataset_params.train_dataset_params.input_dim}
        filter_box_candidates: True   # whether to filter out transformed bboxes by edge size, area ratio, and aspect ratio.
        wh_thr: 2                     # edge size threshold when filter_box_candidates = True (pixels)
        area_thr: 0.1                 # threshold for area ratio between original image and the transformed one, when when filter_box_candidates = True
        ar_thr: 20                    # aspect ratio threshold when filter_box_candidates = True
    - DetectionMixup:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        mixup_scale: [ 0.5, 1.5 ]         # random rescale range for the additional sample in mixup
        prob: 1.0                       # probability to apply per-sample mixup
        flip_prob: 0.5                  # probability to apply horizontal flip
    - DetectionHSV:
        prob: 1.0                       # probability to apply HSV transform
        hgain: 5                        # HSV transform hue gain (randomly sampled from [-hgain, hgain])
        sgain: 30                       # HSV transform saturation gain (randomly sampled from [-sgain, sgain])
        vgain: 30                       # HSV transform value gain (randomly sampled from [-vgain, vgain])
    - DetectionHorizontalFlip:
        prob: 0.5                       # probability to apply horizontal flip
    - DetectionPaddedRescale:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
    - DetectionTargetsFormatTransform:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:
  with_crowd: False

train_dataloader_params:
  shuffle: True
  batch_size: 16
  num_workers: 8
  drop_last: True
  pin_memory: True
  worker_init_fn:
    _target_: super_gradients.training.utils.utils.load_func
    dotpath: super_gradients.training.datasets.datasets_utils.worker_init_reset_seed
  collate_fn: DetectionCollateFN

val_dataset_params:
  data_dir: /data/coco # root path to coco data
  subdir: images/val2017 # sub directory path of data_dir containing the train data.
  json_file: instances_val2017.json # path to coco train json file, data_dir/annotations/train_json_file.
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: True
  transforms:
  - DetectionPaddedRescale:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
  - DetectionTargetsFormatTransform:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
      output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:
  with_crowd: True

val_dataloader_params:
  batch_size: 64
  num_workers: 8
  drop_last: False
  pin_memory: True
  collate_fn: CrowdDetectionCollateFN


_convert_: all
