defaults:
  - cityscapes_dataset_params
  - _self_

train_dataset_params:
  transforms:
    # for more options see common.factories.transforms_factory.py
    - SegColorJitter:
        brightness: 0.5
        contrast: 0.5
        saturation: 0.5

    - SegRandomFlip:
        prob: 0.5

    - SegRandomRescale:
        scales: [ 0.25, 1.75 ]

    - SegPadShortToCropSize:
        crop_size: [ 768, 768 ]
        fill_mask: 19                  # ignored label idx

    - SegCropImageAndMask:
        crop_size: [ 768, 768 ]
        mode: random

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long


val_dataset_params:
  transforms:
    - SegRescale:
        scale_factor: 0.75

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long


train_dataloader_params:
  batch_size: 8

val_dataloader_params:
  batch_size: 4
