defaults:
  - cityscapes_dataset_params
  - _self_

train_dataset_params:
  transforms:
    - SegColorJitter:
        brightness: 0.5
        contrast: 0.5
        saturation: 0.5

    - SegRandomFlip:
        prob: 0.5

    - SegRandomRescale:
        scales: [ 0.5, 2.0 ]

    - SegPadShortToCropSize:
        crop_size: [ 1024, 1024 ]
        fill_mask: 19

    - SegCropImageAndMask:
        crop_size: [ 1024, 1024 ]
        mode: random

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



val_dataset_params:
  transforms:
    - SegRescale:
        long_size: 1024

    - SegPadShortToCropSize:
        crop_size: [ 1024, 1024 ]
        fill_mask: 19

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



train_dataloader_params:
  batch_size: 2
  shuffle: True

val_dataloader_params:
  batch_size: 2
  shuffle: False
