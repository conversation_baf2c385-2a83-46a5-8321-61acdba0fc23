train_dataset_params:
  root_dir: /data/coco/
  list_file: instances_train2017.json
  samples_sub_directory: images/train2017
  targets_sub_directory: annotations
  dataset_classes_inclusion_tuples_list:
    _target_: super_gradients.training.utils.segmentation_utils.coco_sub_classes_inclusion_tuples_list
  cache_labels: False
  cache_images: False
  transforms:
    # for more options see common.factories.transforms_factory.py
    - SegRandomFlip:
        prob: 0.5

    - SegRescale: # consider removing this step
        long_size: 608

    - SegRandomRescale:
        scales: [ 0.5, 2.0 ]

    - SegPadShortToCropSize:
        crop_size: 512

    - SegCropImageAndMask:
        crop_size: 512
        mode: random

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



val_dataset_params:
  root_dir: /data/coco/
  list_file: instances_val2017.json
  samples_sub_directory: images/val2017
  targets_sub_directory: annotations
  dataset_classes_inclusion_tuples_list:
    _target_: super_gradients.training.utils.segmentation_utils.coco_sub_classes_inclusion_tuples_list
  cache_labels: False
  cache_images: False
  transforms:
    - SegRescale:
        short_size: 512

    - SegCropImageAndMask:
        crop_size: 512
        mode: center

    - SegStandardize:
        max_value: 255

    - SegNormalize:
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]

    - SegConvertToTensor:
        mask_output_dtype: long



train_dataloader_params:
  shuffle: True
  batch_size: 8
  num_workers: 8
  drop_last: True                 # drop the last incomplete batch, if dataset size is not divisible by the batch size

val_dataloader_params:
  batch_size: 24
  num_workers: 8
  drop_last: False
