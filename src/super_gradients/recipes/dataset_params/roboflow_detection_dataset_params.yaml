data_dir: /data/rf100

dataset_name: # Set the name of the dataset you want to use (e.g. "digits-t2eg6")

train_dataset_params:
  data_dir: ${..data_dir} # root path to Robflow datasets
  dataset_name: ${..dataset_name}
  split: train
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: False
  transforms:
    - DetectionMosaic:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        prob: 1.
    - DetectionRandomAffine:
        degrees: 0.                  # rotation degrees, randomly sampled from [-degrees, degrees]
        translate: 0.1                # image translation fraction
        scales: [ 0.5, 1.5 ]              # random rescale range (keeps size by padding/cropping) after mosaic transform.
        shear: 0.0                    # shear degrees, randomly sampled from [-degrees, degrees]
        target_size: ${dataset_params.train_dataset_params.input_dim}
        filter_box_candidates: False  # whether to filter out transformed bboxes by edge size, area ratio, and aspect ratio.
        wh_thr: 2                     # edge size threshold when filter_box_candidates = True (pixels)
        area_thr: 0.1                 # threshold for area ratio between original image and the transformed one, when filter_box_candidates = True
        ar_thr: 20                    # aspect ratio threshold when filter_box_candidates = True
        border_value: 128
#    - DetectionMixup:
#        input_dim: ${dataset_params.train_dataset_params.input_dim}
#        mixup_scale: [ 0.5, 1.5 ]         # random rescale range for the additional sample in mixup
#        prob: 1.0                       # probability to apply per-sample mixup
#        flip_prob: 0.5                  # probability to apply horizontal flip
    - DetectionHSV:
        prob: 1.0                       # probability to apply HSV transform
        hgain: 5                        # HSV transform hue gain (randomly sampled from [-hgain, hgain])
        sgain: 30                       # HSV transform saturation gain (randomly sampled from [-sgain, sgain])
        vgain: 30                       # HSV transform value gain (randomly sampled from [-vgain, vgain])
    - DetectionHorizontalFlip:
        prob: 0.5                       # probability to apply horizontal flip
    - DetectionPaddedRescale:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
    - DetectionStandardize:
        max_value: 255.
    - DetectionTargetsFormatTransform:
        input_dim: ${dataset_params.train_dataset_params.input_dim}
        output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:
  with_crowd: False
  verbose: 0

train_dataloader_params:
  shuffle: True
  batch_size: 16
  min_samples: 512
  num_workers: 4
  drop_last: False
  pin_memory: True
  worker_init_fn:
    _target_: super_gradients.training.utils.utils.load_func
    dotpath: super_gradients.training.datasets.datasets_utils.worker_init_reset_seed
  collate_fn: DetectionCollateFN

val_dataset_params:
  data_dir: ${..data_dir} # root path to Robflow datasets
  dataset_name: ${..dataset_name}
  split: valid
  input_dim: [640, 640]
  cache_annotations: True
  ignore_empty_annotations: False
  transforms:
  - DetectionPaddedRescale:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
      pad_value: 114
  - DetectionStandardize:
      max_value: 255.
  - DetectionTargetsFormatTransform:
      input_dim: ${dataset_params.val_dataset_params.input_dim}
      output_format: LABEL_CXCYWH
  class_inclusion_list:
  max_num_samples:
  with_crowd: True
  verbose: 0

val_dataloader_params:
  batch_size: 32
  num_workers: 4
  drop_last: False
  shuffle: False
  pin_memory: True
  collate_fn: CrowdDetectionCollateFN


_convert_: all
