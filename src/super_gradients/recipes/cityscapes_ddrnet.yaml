#  DDRNet segmentation training example with Cityscapes dataset.
#  Paper:
#      "Deep Dual-resolution Networks for Real-time and Accurate Semantic Segmentation of Road Scenes"
#      https://arxiv.org/abs/2104.13188
#


# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#      DDRNet23:        python -m super_gradients.train_from_recipe --config-name=cityscapes_ddrnet
#      DDRNet23-Slim:   python -m super_gradients.train_from_recipe --config-name=cityscapes_ddrnet architecture=ddrnet_23_slim
#      DDRNet39:        python -m super_gradients.train_from_recipe --config-name=cityscapes_ddrnet architecture=ddrnet_39
# Note: add "checkpoint_params.checkpoint_path=<ddrnet23-backbone-pretrained-path>" to use pretrained backbone
#
#  Validation mIoU - Cityscapes, training time:
#      DDRNet23:        input-size: [1024, 2048]     mIoU: 80.26     4 X RTX A5000, 12 H
#      DDRNet23-Slim:   input-size: [1024, 2048]     mIoU: 78.01     4 X RTX A5000, 9 H
#      DDRNet39:        input-size: [1024, 2048]     mIoU: 81.32     4 X RTX A5000, 15 H
#
#  Official git repo:
#      https://github.com/ydhongHIT/DDRNet
#
#  Pretrained checkpoints:
#      Backbones- downloaded from the author's official repo.
#       https://deci-pretrained-models.s3.amazonaws.com/ddrnet/imagenet_pt_backbones/ddrnet23_bb_imagenet.pth
#       https://deci-pretrained-models.s3.amazonaws.com/ddrnet/imagenet_pt_backbones/ddrnet23_slim_bb_imagenet.pth
#       https://deci-pretrained-models.s3.amazonaws.com/ddrnet/imagenet_pt_backbones/ddrnet39_bb_imagenet.pth
#
#      Logs, tensorboards and network checkpoints:
#       DDRNet23:       https://deci-pretrained-models.s3.amazonaws.com/ddrnet/cityscapes/ddrnet23/
#       DDRNet23-Slim:  https://deci-pretrained-models.s3.amazonaws.com/ddrnet/cityscapes/ddrnet23_slim/
#       DDRNet39:       https://deci-pretrained-models.s3.amazonaws.com/ddrnet/cityscapes/ddrnet39/
#
#  Learning rate and batch size parameters, using 4 RTX A5000 with DDP:
#      DDRNet23:        input-size: [1024, 1024]     initial_lr: 0.0075    batch-size: 6 * 4gpus = 24
#      DDRNet23-Slim:   input-size: [1024, 1024]     initial_lr: 0.0075    batch-size: 6 * 4gpus = 24
#      DDRNet39:        input-size: [1024, 1024]     initial_lr: 0.0075    batch-size: 6 * 4gpus = 24
#
#  Comments:
#      * Pretrained backbones were used.

defaults:
  - training_hyperparams: cityscapes_default_train_params
  - dataset_params: cityscapes_ddrnet_dataset_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: cityscapes_train
val_dataloader: cityscapes_val

architecture: ddrnet_23

training_hyperparams:
  max_epochs: 500
  initial_lr: # batch size 24
    default: 0.075
    # backbone layers
    _backbone: 0.0075
    compression3: 0.0075
    compression4: 0.0075
    down3: 0.0075
    down4: 0.0075
    layer3_skip: 0.0075
    layer4_skip: 0.0075
    layer5_skip: 0.0075
  loss:
    DiceCEEdgeLoss:
      num_classes: 19
      ignore_index: 19
      num_aux_heads: 1
      num_detail_heads: 0
      weights: [ 1., 0.4 ]
      dice_ce_weights: [ 1., 1. ]
      ce_edge_weights: [ .5, .5 ]
      edge_kernel: 5
  sync_bn: True


arch_params:
  num_classes: 19
  use_aux_heads: True



load_checkpoint: False
checkpoint_params:
  load_checkpoint: ${load_checkpoint}
  checkpoint_path:
  load_backbone: True
  strict_load: no_key_matching

experiment_name: ${architecture}_cityscapes

multi_gpu: DDP
num_gpus: 4
