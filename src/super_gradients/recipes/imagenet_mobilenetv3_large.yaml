# MobileNetV3 Large Imagenet classification training:
# TODO: Add metrics
#
# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       python -m super_gradients.train_from_recipe --config-name=imagenet_mobilenetv3_large


defaults:
  - imagenet_mobilenetv3_base
  - arch_params: mobilenet_v3_large_arch_params
  - _self_
  - variable_setup

arch_params:
  num_classes: 1000
  dropout: 0.2

experiment_name: mobileNetv3_large_training

architecture: mobilenet_v3_large
