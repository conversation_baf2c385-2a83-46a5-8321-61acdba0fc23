#  STDC segmentation training example with Cityscapes dataset.
#  Reproduction and refinement of paper: Rethinking BiSeNet For Real-time Semantic Segmentation.
#

# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       STDC1-Seg50: python -m super_gradients.train_from_recipe --config-name=cityscapes_stdc_seg50
#       STDC2-Seg50: python -m super_gradients.train_from_recipe --config-name=cityscapes_stdc_seg50 architecture=stdc2_seg
# Note: add "checkpoint_params.checkpoint_path=<stdc1-backbone-pretrained-path>" to use pretrained backbone
#
#
#
#  Validation mIoU - Cityscapes, training time:
#      STDC1-Seg50:    input-size: [512, 1024]     mIoU: 75.11     2 X RTX A5000, 20 H
#      STDC2-Seg50:    input-size: [512, 1024]     mIoU: 76.44     2 X RTX A5000, 23 H
#
#  Official git repo:
#      https://github.com/Michael<PERSON>an01/STDC-Seg
#  Paper:
#      https://arxiv.org/abs/2104.13188
#
#  Pretrained checkpoints:
#      Backbones- downloaded from the author's official repo.
#       https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc1_imagenet_pretrained.pth
#       https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc2_imagenet_pretrained.pth
#
#      Logs, tensorboards and network checkpoints:
#       STDC1-Seg50: https://deci-pretrained-models.s3.amazonaws.com/cityscapes_stdc1_seg50_dice_edge/
#       STDC2-Seg50: https://deci-pretrained-models.s3.amazonaws.com/cityscapes_stdc2_seg50_dice_edge/
#
#  Learning rate and batch size parameters, using 2 RTX A5000 with DDP:
#      STDC1-Seg50:    input-size: [512, 1024]     initial_lr: 0.01    batch-size: 16 * 2gpus = 32
#      STDC2-Seg50:    input-size: [512, 1024]     initial_lr: 0.01    batch-size: 16 * 2gpus = 32
#
#  Comments:
#      * Pretrained backbones were used.

defaults:
  - training_hyperparams: cityscapes_default_train_params
  - dataset_params: cityscapes_stdc_seg50_dataset_params # TODO: uncomment after DatasetInterface refactor
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: cityscapes_train
val_dataloader: cityscapes_val

architecture: stdc1_seg

arch_params:
  num_classes: 19
  use_aux_heads: True

checkpoint_params:
  checkpoint_path:
  load_backbone: True
  load_weights_only: True
  strict_load: no_key_matching

training_hyperparams:
  initial_lr:
    cp: 0.01
    default: 0.1
  sync_bn: True
  loss:
    DiceCEEdgeLoss:
      num_classes: 19
      ignore_index: 19
      weights: [ 1., 0.6, 0.4, 1. ]
      dice_ce_weights: [ 1., 1. ]
      ce_edge_weights: [ .5, .5 ]
      edge_kernel: 3

multi_gpu: DDP
num_gpus: 2

experiment_name: ${architecture}50_cityscapes
