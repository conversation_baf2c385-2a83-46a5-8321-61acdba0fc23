defaults:
  - default_train_params

ema: False
ema_params:
  decay: 0.9997
  decay_type: exp
  beta: 20

max_epochs: 150
lr_mode: CosineLRScheduler
cosine_final_lr_ratio: 0.1
batch_accumulate: 1
initial_lr: 1e-3
loss: DEKRLoss

criterion_params:
  heatmap_loss: qfl
  heatmap_loss_factor: 1.0
  offset_loss_factor: 0.1

mixed_precision: True

optimizer: AdamW
optimizer_params:
  weight_decay: 0.0001
lr_warmup_steps: 256
warmup_initial_lr: 1e-06

valid_metrics_list:
  - PoseEstimationMetrics:
      num_joints: ${dataset_params.num_joints}
      oks_sigmas: ${dataset_params.oks_sigmas}
      max_objects_per_image: 30
      post_prediction_callback:
        _target_: super_gradients.training.utils.pose_estimation.DEKRPoseEstimationDecodeCallback
        max_num_people: 30
        keypoint_threshold: 0.05
        nms_threshold: 0.05
        nms_num_threshold: 8
        output_stride: 4
        apply_sigmoid: True


phase_callbacks: []
#   Note: You can uncomment following block to enable visualization of intermediate results during training.
#   When enabled, these callbacks will save first batch from training & validation to Tensorboard.
#   This is helpful for debugging and doing visual checks whether predictions are reasonable and transforms are
#   working as expected.
#   The only downside is that it tend to bloat Tensorboard logs (Up to ten Gigs for long training regimes).
#  phase_callbacks:
#    - DEKRVisualizationCallback:
#        phase:
#          _target_: super_gradients.training.utils.callbacks.callbacks.Phase
#          value: TRAIN_BATCH_END
#        prefix: "train_"
#        mean: [ 0.485, 0.456, 0.406 ]
#        std: [ 0.229, 0.224, 0.225 ]
#        apply_sigmoid: True
#
#    - DEKRVisualizationCallback:
#        phase:
#          _target_: super_gradients.training.utils.callbacks.callbacks.Phase
#          value: VALIDATION_BATCH_END
#        prefix: "val_"
#        mean: [ 0.485, 0.456, 0.406 ]
#        std: [ 0.229, 0.224, 0.225 ]
#        apply_sigmoid: True


metric_to_watch: 'AP'
greater_metric_to_watch_is_better: True
