# TODO: PRODUC<PERSON> RESULTS AND ADD TENSORBOARDS, LOGS, TRAINING TIME ETC.

defaults:
  - training_hyperparams: imagenet_mobilenetv3_train_params
  - dataset_params: imagenet_mobilenetv3_dataset_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: imagenet_train
val_dataloader: imagenet_val


resume: False
training_hyperparams:
  resume: ${resume}

experiment_name: mobileNetv3_large_training



multi_gpu: DDP
num_gpus: 2

architecture: mobilenet_v3_large
