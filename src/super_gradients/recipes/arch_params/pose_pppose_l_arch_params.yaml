backbone:
  CSPResNetBackbone:
    layers: [ 3, 6, 6, 3 ]                # Backbone's structure
    channels: [ 64, 128, 256, 512, 1024 ] # Number of outputs channels for stem and consecutive feature maps
    activation: silu
    return_idx: [ 0, 1, 2, 3 ]               # Indexes of feature maps to output, indiced 1,2,3 correspond to feature maps of stride 8,16,32
    use_large_stem: True                  # If True, uses 3 conv+bn+act instead of 2 in stem blocks
    use_alpha: False                      # If True, enables additional learnable weighting parameter for 1x1 branch in RepVGGBlock
    depth_mult: 1
    width_mult: 1
    pretrained_weights:

neck:
  CustomCSPPAN:
    out_channels: [768, 384, 192, 128]
    activation: silu
    stage_num: 2
    block_num: 2
    spp: True
    depth_mult: 1
    width_mult: 1

heads:
  LightweightDEKRHead:
    heatmap_channels: 32
    offset_channels_per_joint: 10
    activation: silu
    upscale_factor: 1
    feature_map_index: -1
