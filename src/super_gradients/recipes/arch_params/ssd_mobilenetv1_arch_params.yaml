backbone:
  MobileNetV1Backbone:
    out_layers: [['layers', 9]]

neck:
  SSDBottleneckNeck:
    blocks_out_channels: [1024, 512, 256, 256, 256]
    bottleneck_channels: [256, 256, 128, 128, 128]
    strides: [2, 2, 2, 1, 1]
    kernel_sizes: [3, 3, 3, 3, 2]

heads:
  SSDHead:
    num_classes: 80
    lite: False
    anchors:
      _target_: super_gradients.training.utils.ssd_utils.DefaultBoxes
      fig_size: 320
      feat_size: [40, 20, 10, 5, 3, 2]
      scales: [22, 48, 106, 163, 221, 278, 336]
      aspect_ratios: [[2], [2, 3], [2, 3], [2, 3], [2], [2]]
      scale_xy: 0.1
      scale_wh: 0.2
