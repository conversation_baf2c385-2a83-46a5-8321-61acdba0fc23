# PP-<PERSON>lo-E Detection training on COCO2017 Dataset:
# <PERSON>-<PERSON><PERSON><PERSON><PERSON> trained in 640x640
# Checkpoints + tensorboards: https://deci-pretrained-models.s3.amazonaws.com/ppyoloe_coco/
# Recipe runs with batch size = 24 X 8 gpus = 192.


# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command you want:
#         ppyoloe_s: python -m super_gradients.train_from_recipe --config-name=coco2017_ppyoloe_s
#         ppyoloe_m: python -m super_gradients.train_from_recipe --config-name=coco2017_ppyoloe_m
#         ppyoloe_l: python -m super_gradients.train_from_recipe --config-name=coco2017_ppyoloe_l
#         ppyoloe_x: python -m super_gradients.train_from_recipe --config-name=coco2017_ppyoloe_x
#
# Training times and accuracies (mAP@0.5-0.95 (COCO API, confidence 0.001, IoU threshold 0.6, test on 640x640 images):
#         ppyoloe_s: 37h  on 8 NVIDIA GeForce RTX 3090, mAP: 42.52 (val)
#         ppyoloe_m: 58h  on 8 NVIDIA GeForce RTX 3090, mAP: 47.11 (val)
#         ppyoloe_l: 115h on 8 NVIDIA GeForce RTX 3090, mAP: 49.48 (val)
#         ppyoloe_x: 240h on 8 NVIDIA GeForce RTX 3090, mAP: 51.15 (val)
#

defaults:
  - training_hyperparams: coco2017_ppyoloe_train_params
  - dataset_params: coco_detection_ppyoloe_dataset_params
  - arch_params: ppyoloe_m_arch_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: coco2017_train_ppyoloe
val_dataloader: coco2017_val_ppyoloe

load_checkpoint: False
resume: False

dataset_params:
  train_dataloader_params:
    batch_size: 24

training_hyperparams:
  resume: ${resume}
  mixed_precision: True

  initial_lr:  1e-3

architecture: pp_yoloe_m

multi_gpu: DDP
num_gpus: 8

experiment_suffix: ""
experiment_name: coco2017_${architecture}${experiment_suffix}
