#  PPLiteSeg segmentation training example with Cityscapes dataset.
#  Torch implementation of the paper:
#     <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,
<PERSON>     <PERSON><PERSON>,<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>.
#     PP-LiteSeg: A Superior Real-Time Semantic Segmentation Model.

# Instructions:
#   0. Make sure that the data is stored in dataset_params.dataset_dir or add "dataset_params.data_dir=<PATH-TO-DATASET>" at the end of the command below (feel free to check ReadMe)
#   1. Move to the project root (where you will find the ReadMe and src folder)
#   2. Run the command:
#       PPLite-T-Seg50: python -m super_gradients.train_from_recipe --config-name=cityscapes_pplite_seg50 checkpoint_params.checkpoint_path=<stdc1-backbone-pretrained-path> architecture=pp_lite_t_seg
#       PPLite-B-Seg50: python -m super_gradients.train_from_recipe --config-name=cityscapes_pplite_seg50 checkpoint_params.checkpoint_path=<stdc2-backbone-pretrained-path> architecture=pp_lite_b_seg
#
#
#  Validation mIoU - Cityscapes, training time:
#      PPLite-T-Seg50:    input-size: [512, 1024]     mIoU: 74.92     4 X RTX A5000, 13 H
#      PPLite-B-Seg50:    input-size: [512, 1024]     mIoU: 76.48     4 X RTX A5000, 14 H
#
#  Official git repo:
#      https://github.com/PaddlePaddle/PaddleSeg/
#  Paper:
#      https://arxiv.org/abs/2204.02681
#
#  Pretrained checkpoints:
#      Backbones- downloaded from the STDC author's official repo.
#       PPLite-T-Seg50, (STDC1-backbone):   https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc1_imagenet_pretrained.pth
#       PPLite-B-Seg50, (STDC2-backbone):   https://deci-pretrained-models.s3.amazonaws.com/stdc_backbones/stdc2_imagenet_pretrained.pth
#
#      Logs, tensorboards and network checkpoints:
#       PPLite-T-Seg50: https://deci-pretrained-models.s3.amazonaws.com/ppliteseg/cityscapes/pplite_t_seg50/
#       PPLite-B-Seg50: https://deci-pretrained-models.s3.amazonaws.com/ppliteseg/cityscapes/pplite_b_seg50/
#
#  Learning rate and batch size parameters, using 2 RTX A5000 with DDP:
#      PPLite-T-Seg50:    input-size: [512, 1024]     initial_lr: 0.01    batch-size: 8 * 4gpus = 32
#      PPLite-B-Seg50:    input-size: [512, 1024]     initial_lr: 0.01    batch-size: 8 * 4gpus = 32
#
#  Comments:
#      * ImageNet Pretrained backbones were used.

defaults:
  - training_hyperparams: cityscapes_default_train_params
  - dataset_params: cityscapes_stdc_seg50_dataset_params
  - checkpoint_params: default_checkpoint_params
  - _self_
  - variable_setup

train_dataloader: cityscapes_train
val_dataloader: cityscapes_val

architecture: pp_lite_t_seg

dataset_params:
  train_dataloader_params:
    batch_size: 8
  val_dataloader_params:
    batch_size: 8

arch_params:
  num_classes: 19
  use_aux_heads: True

checkpoint_params:
  checkpoint_path:
  load_backbone: True
  load_weights_only: True
  strict_load: no_key_matching

training_hyperparams:
  sync_bn: True
  initial_lr:
    "encoder.backbone": 0.01
    default: 0.1
  loss:
    DiceCEEdgeLoss:
      num_classes: 19
      ignore_index: 19
      num_aux_heads: 3
      num_detail_heads: 0
      weights: [ 1., 1., 1., 1. ]
      dice_ce_weights: [ 1., 1. ]
      ce_edge_weights: [ .5, .5 ]
      edge_kernel: 5

multi_gpu: DDP
num_gpus: 4

experiment_name: ${architecture}50_cityscapes
