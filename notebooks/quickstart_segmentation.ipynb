{"cells": [{"cell_type": "markdown", "metadata": {"id": "HY_HuQbxn7X0"}, "source": ["![SG - Horizontal.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "oA_p5zIsoAJQ"}, "source": ["# SuperGradients quick start Semantic Segmentation\n", "\n", "In this tutorial we will train PPLiteSeg model on Supervisely semantic segmentation dataset\n", "\n", "The notebook is divided into 7 sections:\n", "1. Experiment setup\n", "2. Dataset definition\n", "3. Architecture definition\n", "4. Training setup\n", "5. Training and Evaluation\n", "6. Predict\n", "7. Convert to ONNX\\TensorRT"]}, {"cell_type": "markdown", "metadata": {"id": "GqH4VGMroWec"}, "source": ["#Install SG"]}, {"cell_type": "markdown", "metadata": {"id": "Q8uA6AWEhHN6"}, "source": ["The cell below will install **super_gradients** which will automatically get all its dependencies."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "-mm-E4xRoNEm"}, "outputs": [], "source": ["! pip install -qq super-gradients==3.7.1"]}, {"cell_type": "markdown", "metadata": {"id": "892xArqDsGsQ"}, "source": ["# 1. Experiment setup\n", "We will initialize our **trainer** which will be in charge of everything, like training, evaluation, saving checkpoints, plotting etc.\n", "\n", "The **experiment name** argument is important as every checkpoints, logs and tensorboards to be saved in a directory with the same name. This directory will be created as a sub-directory of **ckpt_root_dir** as follow:\n", "\n", "```\n", "ckpt_root_dir\n", "|─── experiment_name_1\n", "│       ckpt_best.pth                     # Model checkpoint on best epoch\n", "│       ckpt_latest.pth                   # Model checkpoint on last epoch\n", "│       average_model.pth                 # Model checkpoint averaged over epochs\n", "│       events.out.tfevents.1659878383... # Tensorflow artifacts of a specific run\n", "│       log_Aug07_11_52_48.txt            # Trainer logs of a specific run\n", "└─── experiment_name_2\n", "        ...\n", "```\n", "In this notebook multi-gpu training is set as `OFF`, for Distributed training multi_gpu can be set as\n", " `MultiGPUMode.DISTRIBUTED_DATA_PARALLEL` or `MultiGPUMode.DATA_PARALLEL`."]}, {"cell_type": "markdown", "metadata": {"id": "pl0WPz1HisFz"}, "source": ["Let's define **ckpt_root_dir** inside the Colab, later we can use it to start TensorBoard and monitor the run."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HAff--HysJmP", "outputId": "63e96426-a29b-4cdc-9a72-60da27d6aaa7"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The console stream is logged into /root/sg_logs/console.log\n"]}, {"output_type": "stream", "name": "stderr", "text": ["[2023-11-13 11:11:11] INFO - crash_tips_setup.py - Crash tips is enabled. You can set your environment variable to CRASH_HANDLER=FALSE to disable it\n", "[2023-11-13 11:11:11] WARNING - __init__.py - Failed to import pytorch_quantization\n", "[2023-11-13 11:11:11] INFO - utils.py - NumExpr defaulting to 2 threads.\n", "[2023-11-13 11:11:23] WARNING - calibrator.py - Failed to import pytorch_quantization\n", "[2023-11-13 11:11:23] WARNING - export.py - Failed to import pytorch_quantization\n", "[2023-11-13 11:11:23] WARNING - selective_quantization_utils.py - Failed to import pytorch_quantization\n"]}], "source": ["from super_gradients import Trainer\n", "\n", "CHECKPOINT_DIR = './notebook_ckpts/'\n", "trainer = Trainer(experiment_name=\"segmentation_quick_start\", ckpt_root_dir=CHECKPOINT_DIR)"]}, {"cell_type": "markdown", "metadata": {"id": "dwVMY4gMjQSL"}, "source": ["# 2. Dataset definition\n"]}, {"cell_type": "markdown", "metadata": {"id": "fpIWhnR9j2rm"}, "source": ["\n", "For the sake of this presentation, we'll use **Supervisely** semantic segmentation dataset."]}, {"cell_type": "markdown", "metadata": {"id": "ZACgRb-qjzDJ"}, "source": ["SG trainer is fully compatible with PyTorch data loaders, so you can definitely use your own data for the experiment below if you prefer."]}, {"cell_type": "markdown", "metadata": {"id": "6ulV6Hpao3IN"}, "source": ["## 2.1 Download data\n"]}, {"cell_type": "markdown", "metadata": {"id": "mVwslNv-j-2C"}, "source": ["Feel free to change the download path by editing SUPERVISELY_DATASET_DOWNLOAD_PATH"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "dfR18Rmbo00y"}, "outputs": [], "source": ["import os\n", "\n", "SUPERVISELY_DATASET_DOWNLOAD_PATH=os.path.join(os.getcwd(),\"data\")\n", "\n", "supervisely_dataset_dir_path = os.path.join(SUPERVISELY_DATASET_DOWNLOAD_PATH, 'supervisely-persons')\n", "\n", "if os.path.isdir(supervisely_dataset_dir_path):\n", "    print('supervisely dataset already downloaded...')\n", "else:\n", "    print('Downloading and extracting supervisely dataset to: ' + SUPERVISELY_DATASET_DOWNLOAD_PATH)\n", "    ! mkdir $SUPERVISELY_DATASET_DOWNLOAD_PATH\n", "    %cd $SUPERVISELY_DATASET_DOWNLOAD_PATH\n", "    ! wget https://deci-pretrained-models.s3.amazonaws.com/supervisely-persons.zip\n", "    ! unzip --qq supervisely-persons.zip"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "id": "V9ZcklupX8Qx"}, "source": ["## 2.2 Create data loaders\n"]}, {"cell_type": "markdown", "metadata": {"id": "3Mk_YixjlEhj"}, "source": ["The dataloaders are initiated with the default parameters defined in the [yaml](https://github.com/Deci-AI/super-gradients/blob/master/src/super_gradients/recipes/dataset_params/supervisely_persons_dataset_params.yaml)\n", "file. Parameters as batch_size, transforms, root_dir and others can be overridden by passing as `dataset_params` and\n", "`dataloader_params`, as implemented bellow."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "S3BzMRhSX8Qx", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "87b5092d-fe93-4c0a-8b2e-febe215b52bd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["supervisely dataset already downloaded...\n"]}], "source": ["from super_gradients.training import dataloaders\n", "root_dir = supervisely_dataset_dir_path\n", "batch_size = 8\n", "\n", "train_loader = dataloaders.supervisely_persons_train(dataset_params={\"root_dir\": root_dir}, dataloader_params={\"batch_size\": batch_size})\n", "valid_loader = dataloaders.supervisely_persons_val(dataset_params={\"root_dir\": root_dir}, dataloader_params={\"batch_size\": batch_size})"]}, {"cell_type": "markdown", "metadata": {"id": "6dHIwvs46-dk"}, "source": ["As you can see, we didn't have to pass many parameters into the dataloaders construction. That's because defaults are pre-defined for your convenience, and you might be curious to know what they are. Let's print them and see which resolution and transformations are defined."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "76tzhKxi6aS-", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "3b5c8f34-673c-4f4c-d243-80e82c347f3d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataloader parameters:\n", "{'batch_size': 8, 'shuffle': True, 'drop_last': True}\n", "Dataset parameters\n", "{'root_dir': '/content/data/supervisely-persons', 'list_file': 'train.csv', 'cache_labels': False, 'cache_images': False, 'transforms': [{'SegRandomRescale': {'scales': [0.25, 1.0]}}, {'SegColorJitter': {'brightness': 0.5, 'contrast': 0.5, 'saturation': 0.5}}, {'SegRandomFlip': {'prob': 0.5}}, {'SegPadShortToCropSize': {'crop_size': [320, 480], 'fill_mask': 0}}, {'SegCropImageAndMask': {'crop_size': [320, 480], 'mode': 'random'}}]}\n"]}], "source": ["print('Dataloader parameters:')\n", "print(train_loader.dataloader_params)\n", "print('Dataset parameters')\n", "print(train_loader.dataset.dataset_params)"]}, {"cell_type": "markdown", "metadata": {"id": "l5GcDAg_pUGJ"}, "source": ["# 3. Architecture definition\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "xXPMJQCJzmb4"}, "outputs": [], "source": ["from super_gradients.training import models\n", "from super_gradients.common.object_names import Models\n", "\n", "model = models.get(model_name=Models.PP_LITE_T_SEG,\n", "                   arch_params={\"use_aux_heads\": False},\n", "                   num_classes=1)"]}, {"cell_type": "markdown", "metadata": {"id": "fU8orO7wlwIK"}, "source": ["SG includes implementations of many different architectures for semantic segmentation tasks that can be found [here](https://github.com/Deci-AI/super-gradients#implemented-model-architectures)."]}, {"cell_type": "markdown", "metadata": {"id": "-oGSU3V8lqcm"}, "source": ["Create a PPLiteSeg nn.Module, with 1 class segmentation head classifier. For simplicity `use_aux_head` is set as `False`\n", "and extra Auxiliary heads aren't used for training.\n", "\n", "Other segmentation modules can be used for this task such as, DDRNet, STDC and RegSeg.\n"]}, {"cell_type": "markdown", "metadata": {"id": "X-_dBewgr1dG"}, "source": ["# 4. Training setup\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "H1Rll8Orl-Dy"}, "source": ["\n", "Here we define the training recipe. The full parameters can be found here  [training parameters supported](https://deci-ai.github.io/super-gradients/user_guide.html#training-parameters).\n", "\n", "We will be using an average of BCE and Dice loss for segmentation, with different learning rates for the replaced segmentation head layer, and the rest of the network- this is controlled by the `multiply_head_lr` parameter which is the multiplication factor of the learning rate for the newly replaced layer.\n", "\n", "As our `metric_to_watch`, we will be monitoring the `target_IOU` which is one of the components of `BinaryIOU` torchmetrics object (the other components are `mean_IOU` which is the mean of the background and target IOUs, and `background_IOU`)."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "NShu3zLgr5qD"}, "outputs": [], "source": ["from super_gradients.training.metrics.segmentation_metrics import BinaryIOU\n", "\n", "train_params = {\"max_epochs\": 15,\n", "                \"lr_mode\": \"cosine\",\n", "                \"initial_lr\": 0.01,\n", "                \"lr_warmup_epochs\": 5,\n", "                \"multiply_head_lr\": 10,\n", "                \"optimizer\": \"SGD\",\n", "                \"loss\": \"BCEDiceLoss\",\n", "                \"ema\": True,\n", "                \"ema_params\":\n", "                {\n", "                \"decay\": 0.9999,\n", "                \"decay_type\": \"exp\",\n", "                \"beta\": 15,\n", "                },\n", "\n", "                \"zero_weight_decay_on_bias_and_bn\": True,\n", "                \"average_best_models\": True,\n", "                \"metric_to_watch\": \"target_IOU\",\n", "                \"greater_metric_to_watch_is_better\": True,\n", "                \"train_metrics_list\": [BinaryIOU()],\n", "                \"valid_metrics_list\": [BinaryIOU()],\n", "                \"loss_logging_items_names\": [\"loss\"]\n", "                }"]}, {"cell_type": "markdown", "metadata": {"id": "qTECVyhcs506"}, "source": ["# 5. Training and evaluation\n"]}, {"cell_type": "markdown", "metadata": {"id": "S1K5MU2kmmDb"}, "source": ["The logs and the checkpoint for the latest epoch will be kept in your experiment folder.\n", "\n", "To start training we'll call train(...) and provide it with the objects we construted above: the model, the training parameters and the data loaders.\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "u6roEj9ktFTi", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4a295f63-f0c4-43a7-c6e8-2f7ffd1b5ce2"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[2023-11-13 11:15:07] INFO - sg_trainer.py - Starting a new run with `run_id=RUN_20231113_111507_197271`\n", "[2023-11-13 11:15:07] INFO - sg_trainer.py - Checkpoints directory: ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271\n", "[2023-11-13 11:15:07] INFO - sg_trainer.py - Using EMA with params {'decay': 0.9999, 'decay_type': 'exp', 'beta': 15}\n"]}, {"output_type": "stream", "name": "stdout", "text": ["The console stream is now moved to ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/console_Nov13_11_15_07.txt\n"]}, {"output_type": "stream", "name": "stderr", "text": ["[2023-11-13 11:15:08] INFO - sg_trainer_utils.py - TRAINING PARAMETERS:\n", "    - Mode:                         Single GPU\n", "    - Number of GPUs:               1          (1 available on the machine)\n", "    - Full dataset size:            2477       (len(train_set))\n", "    - Batch size per GPU:           8          (batch_size)\n", "    - Batch Accumulate:             1          (batch_accumulate)\n", "    - Total batch size:             8          (num_gpus * batch_size)\n", "    - Effective Batch size:         8          (num_gpus * batch_size * batch_accumulate)\n", "    - Iterations per epoch:         309        (len(train_loader))\n", "    - Gradient updates per epoch:   309        (len(train_loader) / batch_accumulate)\n", "\n", "[2023-11-13 11:15:08] INFO - sg_trainer.py - Started training for 15 epochs (0/14)\n", "\n", "Train epoch 0: 100%|██████████| 309/309 [02:12<00:00,  2.33it/s, BCEDiceLoss=0.4, background_IOU=0.545, gpu_mem=1.14, mean_IOU=0.609, target_IOU=0.674]\n", "Validating: 100%|██████████| 65/65 [00:17<00:00,  3.69it/s]\n", "[2023-11-13 11:17:39] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:17:39] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.6779429912567139\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 0\n", "├── Train\n", "│   ├── Bcediceloss = 0.4001\n", "│   ├── Target_iou = 0.6736\n", "│   ├── Background_iou = 0.5448\n", "│   └── Mean_iou = 0.6092\n", "└── Validation\n", "    ├── Bcediceloss = 0.4166\n", "    ├── Target_iou = 0.6779\n", "    ├── Background_iou = 0.4039\n", "    └── Mean_iou = 0.5409\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 1: 100%|██████████| 309/309 [02:05<00:00,  2.46it/s, BCEDiceLoss=0.338, background_IOU=0.604, gpu_mem=1.14, mean_IOU=0.661, target_IOU=0.719]\n", "Validating epoch 1: 100%|██████████| 65/65 [00:17<00:00,  3.69it/s]\n", "[2023-11-13 11:20:05] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:20:05] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7205255031585693\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 1\n", "├── Train\n", "│   ├── Bcediceloss = 0.3381\n", "│   │   ├── Epoch N-1      = 0.4001 (\u001b[32m↘ -0.062\u001b[0m)\n", "│   │   └── Best until now = 0.4001 (\u001b[32m↘ -0.062\u001b[0m)\n", "│   ├── Target_iou = 0.7193\n", "│   │   ├── Epoch N-1      = 0.6736 (\u001b[32m↗ 0.0457\u001b[0m)\n", "│   │   └── Best until now = 0.6736 (\u001b[32m↗ 0.0457\u001b[0m)\n", "│   ├── Background_iou = 0.6036\n", "│   │   ├── Epoch N-1      = 0.5448 (\u001b[32m↗ 0.0587\u001b[0m)\n", "│   │   └── Best until now = 0.5448 (\u001b[32m↗ 0.0587\u001b[0m)\n", "│   └── Mean_iou = 0.6614\n", "│       ├── Epoch N-1      = 0.6092 (\u001b[32m↗ 0.0522\u001b[0m)\n", "│       └── Best until now = 0.6092 (\u001b[32m↗ 0.0522\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3578\n", "    │   ├── Epoch N-1      = 0.4166 (\u001b[32m↘ -0.0588\u001b[0m)\n", "    │   └── Best until now = 0.4166 (\u001b[32m↘ -0.0588\u001b[0m)\n", "    ├── Target_iou = 0.7205\n", "    │   ├── Epoch N-1      = 0.6779 (\u001b[32m↗ 0.0426\u001b[0m)\n", "    │   └── Best until now = 0.6779 (\u001b[32m↗ 0.0426\u001b[0m)\n", "    ├── Background_iou = 0.4497\n", "    │   ├── Epoch N-1      = 0.4039 (\u001b[32m↗ 0.0458\u001b[0m)\n", "    │   └── Best until now = 0.4039 (\u001b[32m↗ 0.0458\u001b[0m)\n", "    └── Mean_iou = 0.5851\n", "        ├── Epoch N-1      = 0.5409 (\u001b[32m↗ 0.0442\u001b[0m)\n", "        └── Best until now = 0.5409 (\u001b[32m↗ 0.0442\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 2: 100%|██████████| 309/309 [02:00<00:00,  2.55it/s, BCEDiceLoss=0.32, background_IOU=0.634, gpu_mem=1.14, mean_IOU=0.684, target_IOU=0.734]\n", "Validating epoch 2: 100%|██████████| 65/65 [00:16<00:00,  3.84it/s]\n", "[2023-11-13 11:22:24] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:22:24] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7300039529800415\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 2\n", "├── Train\n", "│   ├── Bcediceloss = 0.3199\n", "│   │   ├── Epoch N-1      = 0.3381 (\u001b[32m↘ -0.0182\u001b[0m)\n", "│   │   └── Best until now = 0.3381 (\u001b[32m↘ -0.0182\u001b[0m)\n", "│   ├── Target_iou = 0.734\n", "│   │   ├── Epoch N-1      = 0.7193 (\u001b[32m↗ 0.0147\u001b[0m)\n", "│   │   └── Best until now = 0.7193 (\u001b[32m↗ 0.0147\u001b[0m)\n", "│   ├── Background_iou = 0.6344\n", "│   │   ├── Epoch N-1      = 0.6036 (\u001b[32m↗ 0.0308\u001b[0m)\n", "│   │   └── Best until now = 0.6036 (\u001b[32m↗ 0.0308\u001b[0m)\n", "│   └── Mean_iou = 0.6842\n", "│       ├── Epoch N-1      = 0.6614 (\u001b[32m↗ 0.0227\u001b[0m)\n", "│       └── Best until now = 0.6614 (\u001b[32m↗ 0.0227\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.357\n", "    │   ├── Epoch N-1      = 0.3578 (\u001b[32m↘ -0.0008\u001b[0m)\n", "    │   └── Best until now = 0.3578 (\u001b[32m↘ -0.0008\u001b[0m)\n", "    ├── Target_iou = 0.73\n", "    │   ├── Epoch N-1      = 0.7205 (\u001b[32m↗ 0.0095\u001b[0m)\n", "    │   └── Best until now = 0.7205 (\u001b[32m↗ 0.0095\u001b[0m)\n", "    ├── Background_iou = 0.4503\n", "    │   ├── Epoch N-1      = 0.4497 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    │   └── Best until now = 0.4497 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    └── Mean_iou = 0.5902\n", "        ├── Epoch N-1      = 0.5851 (\u001b[32m↗ 0.0051\u001b[0m)\n", "        └── Best until now = 0.5851 (\u001b[32m↗ 0.0051\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 3: 100%|██████████| 309/309 [01:59<00:00,  2.58it/s, BCEDiceLoss=0.302, background_IOU=0.645, gpu_mem=1.14, mean_IOU=0.697, target_IOU=0.75]\n", "Validating epoch 3: 100%|██████████| 65/65 [00:16<00:00,  3.84it/s]\n", "[2023-11-13 11:24:43] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:24:43] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7432040572166443\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 3\n", "├── Train\n", "│   ├── Bcediceloss = 0.3022\n", "│   │   ├── Epoch N-1      = 0.3199 (\u001b[32m↘ -0.0177\u001b[0m)\n", "│   │   └── Best until now = 0.3199 (\u001b[32m↘ -0.0177\u001b[0m)\n", "│   ├── Target_iou = 0.7501\n", "│   │   ├── Epoch N-1      = 0.734  (\u001b[32m↗ 0.0161\u001b[0m)\n", "│   │   └── Best until now = 0.734  (\u001b[32m↗ 0.0161\u001b[0m)\n", "│   ├── Background_iou = 0.6447\n", "│   │   ├── Epoch N-1      = 0.6344 (\u001b[32m↗ 0.0103\u001b[0m)\n", "│   │   └── Best until now = 0.6344 (\u001b[32m↗ 0.0103\u001b[0m)\n", "│   └── Mean_iou = 0.6974\n", "│       ├── Epoch N-1      = 0.6842 (\u001b[32m↗ 0.0132\u001b[0m)\n", "│       └── Best until now = 0.6842 (\u001b[32m↗ 0.0132\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3307\n", "    │   ├── Epoch N-1      = 0.357  (\u001b[32m↘ -0.0263\u001b[0m)\n", "    │   └── Best until now = 0.357  (\u001b[32m↘ -0.0263\u001b[0m)\n", "    ├── Target_iou = 0.7432\n", "    │   ├── Epoch N-1      = 0.73   (\u001b[32m↗ 0.0132\u001b[0m)\n", "    │   └── Best until now = 0.73   (\u001b[32m↗ 0.0132\u001b[0m)\n", "    ├── Background_iou = 0.4794\n", "    │   ├── Epoch N-1      = 0.4503 (\u001b[32m↗ 0.0291\u001b[0m)\n", "    │   └── Best until now = 0.4503 (\u001b[32m↗ 0.0291\u001b[0m)\n", "    └── Mean_iou = 0.6113\n", "        ├── Epoch N-1      = 0.5902 (\u001b[32m↗ 0.0212\u001b[0m)\n", "        └── Best until now = 0.5902 (\u001b[32m↗ 0.0212\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 4: 100%|██████████| 309/309 [02:00<00:00,  2.56it/s, BCEDiceLoss=0.287, background_IOU=0.67, gpu_mem=1.14, mean_IOU=0.715, target_IOU=0.76]\n", "Validating epoch 4: 100%|██████████| 65/65 [00:17<00:00,  3.79it/s]\n", "[2023-11-13 11:27:02] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:27:02] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7445915341377258\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 4\n", "├── Train\n", "│   ├── Bcediceloss = 0.2867\n", "│   │   ├── Epoch N-1      = 0.3022 (\u001b[32m↘ -0.0155\u001b[0m)\n", "│   │   └── Best until now = 0.3022 (\u001b[32m↘ -0.0155\u001b[0m)\n", "│   ├── Target_iou = 0.7604\n", "│   │   ├── Epoch N-1      = 0.7501 (\u001b[32m↗ 0.0103\u001b[0m)\n", "│   │   └── Best until now = 0.7501 (\u001b[32m↗ 0.0103\u001b[0m)\n", "│   ├── Background_iou = 0.6697\n", "│   │   ├── Epoch N-1      = 0.6447 (\u001b[32m↗ 0.0251\u001b[0m)\n", "│   │   └── Best until now = 0.6447 (\u001b[32m↗ 0.0251\u001b[0m)\n", "│   └── Mean_iou = 0.715\n", "│       ├── Epoch N-1      = 0.6974 (\u001b[32m↗ 0.0177\u001b[0m)\n", "│       └── Best until now = 0.6974 (\u001b[32m↗ 0.0177\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3281\n", "    │   ├── Epoch N-1      = 0.3307 (\u001b[32m↘ -0.0026\u001b[0m)\n", "    │   └── Best until now = 0.3307 (\u001b[32m↘ -0.0026\u001b[0m)\n", "    ├── Target_iou = 0.7446\n", "    │   ├── Epoch N-1      = 0.7432 (\u001b[32m↗ 0.0014\u001b[0m)\n", "    │   └── Best until now = 0.7432 (\u001b[32m↗ 0.0014\u001b[0m)\n", "    ├── Background_iou = 0.4869\n", "    │   ├── Epoch N-1      = 0.4794 (\u001b[32m↗ 0.0074\u001b[0m)\n", "    │   └── Best until now = 0.4794 (\u001b[32m↗ 0.0074\u001b[0m)\n", "    └── Mean_iou = 0.6157\n", "        ├── Epoch N-1      = 0.6113 (\u001b[32m↗ 0.0044\u001b[0m)\n", "        └── Best until now = 0.6113 (\u001b[32m↗ 0.0044\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 5: 100%|██████████| 309/309 [02:02<00:00,  2.53it/s, BCEDiceLoss=0.287, background_IOU=0.664, gpu_mem=1.14, mean_IOU=0.712, target_IOU=0.761]\n", "Validating epoch 5: 100%|██████████| 65/65 [00:17<00:00,  3.75it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 5\n", "├── Train\n", "│   ├── Bcediceloss = 0.2869\n", "│   │   ├── Epoch N-1      = 0.2867 (\u001b[31m↗ 1e-04\u001b[0m)\n", "│   │   └── Best until now = 0.2867 (\u001b[31m↗ 1e-04\u001b[0m)\n", "│   ├── Target_iou = 0.7606\n", "│   │   ├── Epoch N-1      = 0.7604 (\u001b[32m↗ 0.0002\u001b[0m)\n", "│   │   └── Best until now = 0.7604 (\u001b[32m↗ 0.0002\u001b[0m)\n", "│   ├── Background_iou = 0.6637\n", "│   │   ├── Epoch N-1      = 0.6697 (\u001b[31m↘ -0.0061\u001b[0m)\n", "│   │   └── Best until now = 0.6697 (\u001b[31m↘ -0.0061\u001b[0m)\n", "│   └── Mean_iou = 0.7121\n", "│       ├── Epoch N-1      = 0.715  (\u001b[31m↘ -0.0029\u001b[0m)\n", "│       └── Best until now = 0.715  (\u001b[31m↘ -0.0029\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3339\n", "    │   ├── Epoch N-1      = 0.3281 (\u001b[31m↗ 0.0059\u001b[0m)\n", "    │   └── Best until now = 0.3281 (\u001b[31m↗ 0.0059\u001b[0m)\n", "    ├── Target_iou = 0.7402\n", "    │   ├── Epoch N-1      = 0.7446 (\u001b[31m↘ -0.0044\u001b[0m)\n", "    │   └── Best until now = 0.7446 (\u001b[31m↘ -0.0044\u001b[0m)\n", "    ├── Background_iou = 0.4593\n", "    │   ├── Epoch N-1      = 0.4869 (\u001b[31m↘ -0.0276\u001b[0m)\n", "    │   └── Best until now = 0.4869 (\u001b[31m↘ -0.0276\u001b[0m)\n", "    └── Mean_iou = 0.5997\n", "        ├── Epoch N-1      = 0.6157 (\u001b[31m↘ -0.016\u001b[0m)\n", "        └── Best until now = 0.6157 (\u001b[31m↘ -0.016\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 6: 100%|██████████| 309/309 [02:03<00:00,  2.50it/s, BCEDiceLoss=0.269, background_IOU=0.689, gpu_mem=1.14, mean_IOU=0.731, target_IOU=0.772]\n", "Validating epoch 6: 100%|██████████| 65/65 [00:17<00:00,  3.77it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 6\n", "├── Train\n", "│   ├── Bcediceloss = 0.2686\n", "│   │   ├── Epoch N-1      = 0.2869 (\u001b[32m↘ -0.0183\u001b[0m)\n", "│   │   └── Best until now = 0.2867 (\u001b[32m↘ -0.0181\u001b[0m)\n", "│   ├── Target_iou = 0.7721\n", "│   │   ├── Epoch N-1      = 0.7606 (\u001b[32m↗ 0.0115\u001b[0m)\n", "│   │   └── Best until now = 0.7606 (\u001b[32m↗ 0.0115\u001b[0m)\n", "│   ├── Background_iou = 0.6892\n", "│   │   ├── Epoch N-1      = 0.6637 (\u001b[32m↗ 0.0255\u001b[0m)\n", "│   │   └── Best until now = 0.6697 (\u001b[32m↗ 0.0194\u001b[0m)\n", "│   └── Mean_iou = 0.7306\n", "│       ├── Epoch N-1      = 0.7121 (\u001b[32m↗ 0.0185\u001b[0m)\n", "│       └── Best until now = 0.715  (\u001b[32m↗ 0.0156\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3278\n", "    │   ├── Epoch N-1      = 0.3339 (\u001b[32m↘ -0.0061\u001b[0m)\n", "    │   └── Best until now = 0.3281 (\u001b[32m↘ -0.0003\u001b[0m)\n", "    ├── Target_iou = 0.7431\n", "    │   ├── Epoch N-1      = 0.7402 (\u001b[32m↗ 0.003\u001b[0m)\n", "    │   └── Best until now = 0.7446 (\u001b[31m↘ -0.0015\u001b[0m)\n", "    ├── Background_iou = 0.4733\n", "    │   ├── Epoch N-1      = 0.4593 (\u001b[32m↗ 0.0139\u001b[0m)\n", "    │   └── Best until now = 0.4869 (\u001b[31m↘ -0.0136\u001b[0m)\n", "    └── Mean_iou = 0.6082\n", "        ├── Epoch N-1      = 0.5997 (\u001b[32m↗ 0.0085\u001b[0m)\n", "        └── Best until now = 0.6157 (\u001b[31m↘ -0.0075\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 7: 100%|██████████| 309/309 [02:01<00:00,  2.54it/s, BCEDiceLoss=0.259, background_IOU=0.701, gpu_mem=1.14, mean_IOU=0.741, target_IOU=0.781]\n", "Validating epoch 7: 100%|██████████| 65/65 [00:17<00:00,  3.77it/s]\n", "[2023-11-13 11:34:05] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:34:05] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7548585534095764\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 7\n", "├── Train\n", "│   ├── Bcediceloss = 0.259\n", "│   │   ├── Epoch N-1      = 0.2686 (\u001b[32m↘ -0.0096\u001b[0m)\n", "│   │   └── Best until now = 0.2686 (\u001b[32m↘ -0.0096\u001b[0m)\n", "│   ├── Target_iou = 0.7808\n", "│   │   ├── Epoch N-1      = 0.7721 (\u001b[32m↗ 0.0087\u001b[0m)\n", "│   │   └── Best until now = 0.7721 (\u001b[32m↗ 0.0087\u001b[0m)\n", "│   ├── Background_iou = 0.7009\n", "│   │   ├── Epoch N-1      = 0.6892 (\u001b[32m↗ 0.0117\u001b[0m)\n", "│   │   └── Best until now = 0.6892 (\u001b[32m↗ 0.0117\u001b[0m)\n", "│   └── Mean_iou = 0.7409\n", "│       ├── Epoch N-1      = 0.7306 (\u001b[32m↗ 0.0102\u001b[0m)\n", "│       └── Best until now = 0.7306 (\u001b[32m↗ 0.0102\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3129\n", "    │   ├── Epoch N-1      = 0.3278 (\u001b[32m↘ -0.0149\u001b[0m)\n", "    │   └── Best until now = 0.3278 (\u001b[32m↘ -0.0149\u001b[0m)\n", "    ├── Target_iou = 0.7549\n", "    │   ├── Epoch N-1      = 0.7431 (\u001b[32m↗ 0.0117\u001b[0m)\n", "    │   └── Best until now = 0.7446 (\u001b[32m↗ 0.0103\u001b[0m)\n", "    ├── Background_iou = 0.5241\n", "    │   ├── Epoch N-1      = 0.4733 (\u001b[32m↗ 0.0508\u001b[0m)\n", "    │   └── Best until now = 0.4869 (\u001b[32m↗ 0.0372\u001b[0m)\n", "    └── Mean_iou = 0.6395\n", "        ├── Epoch N-1      = 0.6082 (\u001b[32m↗ 0.0313\u001b[0m)\n", "        └── Best until now = 0.6157 (\u001b[32m↗ 0.0238\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 8: 100%|██████████| 309/309 [02:05<00:00,  2.47it/s, BCEDiceLoss=0.251, background_IOU=0.713, gpu_mem=1.14, mean_IOU=0.749, target_IOU=0.786]\n", "Validating epoch 8: 100%|██████████| 65/65 [00:17<00:00,  3.77it/s]\n", "[2023-11-13 11:36:30] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:36:30] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7585687637329102\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 8\n", "├── Train\n", "│   ├── Bcediceloss = 0.251\n", "│   │   ├── Epoch N-1      = 0.259  (\u001b[32m↘ -0.008\u001b[0m)\n", "│   │   └── Best until now = 0.259  (\u001b[32m↘ -0.008\u001b[0m)\n", "│   ├── Target_iou = 0.786\n", "│   │   ├── Epoch N-1      = 0.7808 (\u001b[32m↗ 0.0052\u001b[0m)\n", "│   │   └── Best until now = 0.7808 (\u001b[32m↗ 0.0052\u001b[0m)\n", "│   ├── Background_iou = 0.7125\n", "│   │   ├── Epoch N-1      = 0.7009 (\u001b[32m↗ 0.0116\u001b[0m)\n", "│   │   └── Best until now = 0.7009 (\u001b[32m↗ 0.0116\u001b[0m)\n", "│   └── Mean_iou = 0.7493\n", "│       ├── Epoch N-1      = 0.7409 (\u001b[32m↗ 0.0084\u001b[0m)\n", "│       └── Best until now = 0.7409 (\u001b[32m↗ 0.0084\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3091\n", "    │   ├── Epoch N-1      = 0.3129 (\u001b[32m↘ -0.0039\u001b[0m)\n", "    │   └── Best until now = 0.3129 (\u001b[32m↘ -0.0039\u001b[0m)\n", "    ├── Target_iou = 0.7586\n", "    │   ├── Epoch N-1      = 0.7549 (\u001b[32m↗ 0.0037\u001b[0m)\n", "    │   └── Best until now = 0.7549 (\u001b[32m↗ 0.0037\u001b[0m)\n", "    ├── Background_iou = 0.5411\n", "    │   ├── Epoch N-1      = 0.5241 (\u001b[32m↗ 0.017\u001b[0m)\n", "    │   └── Best until now = 0.5241 (\u001b[32m↗ 0.017\u001b[0m)\n", "    └── Mean_iou = 0.6498\n", "        ├── Epoch N-1      = 0.6395 (\u001b[32m↗ 0.0103\u001b[0m)\n", "        └── Best until now = 0.6395 (\u001b[32m↗ 0.0103\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 9: 100%|██████████| 309/309 [02:02<00:00,  2.53it/s, BCEDiceLoss=0.246, background_IOU=0.713, gpu_mem=1.14, mean_IOU=0.752, target_IOU=0.791]\n", "Validating epoch 9: 100%|██████████| 65/65 [00:17<00:00,  3.72it/s]\n", "[2023-11-13 11:38:53] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:38:53] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.759834885597229\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 9\n", "├── Train\n", "│   ├── Bcediceloss = 0.2465\n", "│   │   ├── Epoch N-1      = 0.251  (\u001b[32m↘ -0.0045\u001b[0m)\n", "│   │   └── Best until now = 0.251  (\u001b[32m↘ -0.0045\u001b[0m)\n", "│   ├── Target_iou = 0.7905\n", "│   │   ├── Epoch N-1      = 0.786  (\u001b[32m↗ 0.0045\u001b[0m)\n", "│   │   └── Best until now = 0.786  (\u001b[32m↗ 0.0045\u001b[0m)\n", "│   ├── Background_iou = 0.7133\n", "│   │   ├── Epoch N-1      = 0.7125 (\u001b[32m↗ 0.0008\u001b[0m)\n", "│   │   └── Best until now = 0.7125 (\u001b[32m↗ 0.0008\u001b[0m)\n", "│   └── Mean_iou = 0.7519\n", "│       ├── Epoch N-1      = 0.7493 (\u001b[32m↗ 0.0026\u001b[0m)\n", "│       └── Best until now = 0.7493 (\u001b[32m↗ 0.0026\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3072\n", "    │   ├── Epoch N-1      = 0.3091 (\u001b[32m↘ -0.0018\u001b[0m)\n", "    │   └── Best until now = 0.3091 (\u001b[32m↘ -0.0018\u001b[0m)\n", "    ├── Target_iou = 0.7598\n", "    │   ├── Epoch N-1      = 0.7586 (\u001b[32m↗ 0.0013\u001b[0m)\n", "    │   └── Best until now = 0.7586 (\u001b[32m↗ 0.0013\u001b[0m)\n", "    ├── Background_iou = 0.5481\n", "    │   ├── Epoch N-1      = 0.5411 (\u001b[32m↗ 0.007\u001b[0m)\n", "    │   └── Best until now = 0.5411 (\u001b[32m↗ 0.007\u001b[0m)\n", "    └── Mean_iou = 0.6539\n", "        ├── Epoch N-1      = 0.6498 (\u001b[32m↗ 0.0041\u001b[0m)\n", "        └── Best until now = 0.6498 (\u001b[32m↗ 0.0041\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 10: 100%|██████████| 309/309 [02:03<00:00,  2.50it/s, BCEDiceLoss=0.24, background_IOU=0.723, gpu_mem=1.14, mean_IOU=0.759, target_IOU=0.796]\n", "Validating epoch 10: 100%|██████████| 65/65 [00:17<00:00,  3.77it/s]\n", "[2023-11-13 11:41:16] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:41:16] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7605207562446594\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 10\n", "├── Train\n", "│   ├── Bcediceloss = 0.2399\n", "│   │   ├── Epoch N-1      = 0.2465 (\u001b[32m↘ -0.0066\u001b[0m)\n", "│   │   └── Best until now = 0.2465 (\u001b[32m↘ -0.0066\u001b[0m)\n", "│   ├── Target_iou = 0.7956\n", "│   │   ├── Epoch N-1      = 0.7905 (\u001b[32m↗ 0.0051\u001b[0m)\n", "│   │   └── Best until now = 0.7905 (\u001b[32m↗ 0.0051\u001b[0m)\n", "│   ├── Background_iou = 0.7229\n", "│   │   ├── Epoch N-1      = 0.7133 (\u001b[32m↗ 0.0096\u001b[0m)\n", "│   │   └── Best until now = 0.7133 (\u001b[32m↗ 0.0096\u001b[0m)\n", "│   └── Mean_iou = 0.7593\n", "│       ├── Epoch N-1      = 0.7519 (\u001b[32m↗ 0.0074\u001b[0m)\n", "│       └── Best until now = 0.7519 (\u001b[32m↗ 0.0074\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3059\n", "    │   ├── Epoch N-1      = 0.3072 (\u001b[32m↘ -0.0014\u001b[0m)\n", "    │   └── Best until now = 0.3072 (\u001b[32m↘ -0.0014\u001b[0m)\n", "    ├── Target_iou = 0.7605\n", "    │   ├── Epoch N-1      = 0.7598 (\u001b[32m↗ 0.0007\u001b[0m)\n", "    │   └── Best until now = 0.7598 (\u001b[32m↗ 0.0007\u001b[0m)\n", "    ├── Background_iou = 0.5517\n", "    │   ├── Epoch N-1      = 0.5481 (\u001b[32m↗ 0.0037\u001b[0m)\n", "    │   └── Best until now = 0.5481 (\u001b[32m↗ 0.0037\u001b[0m)\n", "    └── Mean_iou = 0.6561\n", "        ├── Epoch N-1      = 0.6539 (\u001b[32m↗ 0.0022\u001b[0m)\n", "        └── Best until now = 0.6539 (\u001b[32m↗ 0.0022\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 11: 100%|██████████| 309/309 [02:01<00:00,  2.54it/s, BCEDiceLoss=0.231, background_IOU=0.733, gpu_mem=1.14, mean_IOU=0.767, target_IOU=0.801]\n", "Validating epoch 11: 100%|██████████| 65/65 [00:17<00:00,  3.76it/s]\n", "[2023-11-13 11:43:37] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:43:37] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7611058950424194\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 11\n", "├── Train\n", "│   ├── Bcediceloss = 0.2309\n", "│   │   ├── Epoch N-1      = 0.2399 (\u001b[32m↘ -0.009\u001b[0m)\n", "│   │   └── Best until now = 0.2399 (\u001b[32m↘ -0.009\u001b[0m)\n", "│   ├── Target_iou = 0.8015\n", "│   │   ├── Epoch N-1      = 0.7956 (\u001b[32m↗ 0.0059\u001b[0m)\n", "│   │   └── Best until now = 0.7956 (\u001b[32m↗ 0.0059\u001b[0m)\n", "│   ├── Background_iou = 0.7333\n", "│   │   ├── Epoch N-1      = 0.7229 (\u001b[32m↗ 0.0104\u001b[0m)\n", "│   │   └── Best until now = 0.7229 (\u001b[32m↗ 0.0104\u001b[0m)\n", "│   └── Mean_iou = 0.7674\n", "│       ├── Epoch N-1      = 0.7593 (\u001b[32m↗ 0.0081\u001b[0m)\n", "│       └── Best until now = 0.7593 (\u001b[32m↗ 0.0081\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3046\n", "    │   ├── Epoch N-1      = 0.3059 (\u001b[32m↘ -0.0012\u001b[0m)\n", "    │   └── Best until now = 0.3059 (\u001b[32m↘ -0.0012\u001b[0m)\n", "    ├── Target_iou = 0.7611\n", "    │   ├── Epoch N-1      = 0.7605 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    │   └── Best until now = 0.7605 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    ├── Background_iou = 0.5546\n", "    │   ├── Epoch N-1      = 0.5517 (\u001b[32m↗ 0.0029\u001b[0m)\n", "    │   └── Best until now = 0.5517 (\u001b[32m↗ 0.0029\u001b[0m)\n", "    └── Mean_iou = 0.6579\n", "        ├── Epoch N-1      = 0.6561 (\u001b[32m↗ 0.0017\u001b[0m)\n", "        └── Best until now = 0.6561 (\u001b[32m↗ 0.0017\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 12: 100%|██████████| 309/309 [02:03<00:00,  2.51it/s, BCEDiceLoss=0.224, background_IOU=0.736, gpu_mem=1.14, mean_IOU=0.771, target_IOU=0.807]\n", "Validating epoch 12: 100%|██████████| 65/65 [00:17<00:00,  3.77it/s]\n", "[2023-11-13 11:46:00] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:46:00] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7616798877716064\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 12\n", "├── Train\n", "│   ├── Bcediceloss = 0.2243\n", "│   │   ├── Epoch N-1      = 0.2309 (\u001b[32m↘ -0.0066\u001b[0m)\n", "│   │   └── Best until now = 0.2309 (\u001b[32m↘ -0.0066\u001b[0m)\n", "│   ├── Target_iou = 0.8068\n", "│   │   ├── Epoch N-1      = 0.8015 (\u001b[32m↗ 0.0053\u001b[0m)\n", "│   │   └── Best until now = 0.8015 (\u001b[32m↗ 0.0053\u001b[0m)\n", "│   ├── Background_iou = 0.736\n", "│   │   ├── Epoch N-1      = 0.7333 (\u001b[32m↗ 0.0027\u001b[0m)\n", "│   │   └── Best until now = 0.7333 (\u001b[32m↗ 0.0027\u001b[0m)\n", "│   └── Mean_iou = 0.7714\n", "│       ├── Epoch N-1      = 0.7674 (\u001b[32m↗ 0.004\u001b[0m)\n", "│       └── Best until now = 0.7674 (\u001b[32m↗ 0.004\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3035\n", "    │   ├── Epoch N-1      = 0.3046 (\u001b[32m↘ -0.0012\u001b[0m)\n", "    │   └── Best until now = 0.3046 (\u001b[32m↘ -0.0012\u001b[0m)\n", "    ├── Target_iou = 0.7617\n", "    │   ├── Epoch N-1      = 0.7611 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    │   └── Best until now = 0.7611 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    ├── Background_iou = 0.5569\n", "    │   ├── Epoch N-1      = 0.5546 (\u001b[32m↗ 0.0023\u001b[0m)\n", "    │   └── Best until now = 0.5546 (\u001b[32m↗ 0.0023\u001b[0m)\n", "    └── Mean_iou = 0.6593\n", "        ├── Epoch N-1      = 0.6579 (\u001b[32m↗ 0.0014\u001b[0m)\n", "        └── Best until now = 0.6579 (\u001b[32m↗ 0.0014\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 13: 100%|██████████| 309/309 [02:01<00:00,  2.55it/s, BCEDiceLoss=0.219, background_IOU=0.745, gpu_mem=1.14, mean_IOU=0.777, target_IOU=0.81]\n", "Validating epoch 13: 100%|██████████| 65/65 [00:17<00:00,  3.81it/s]\n", "[2023-11-13 11:48:23] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:48:23] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.7624021172523499\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 13\n", "├── Train\n", "│   ├── Bcediceloss = 0.2194\n", "│   │   ├── Epoch N-1      = 0.2243 (\u001b[32m↘ -0.0049\u001b[0m)\n", "│   │   └── Best until now = 0.2243 (\u001b[32m↘ -0.0049\u001b[0m)\n", "│   ├── Target_iou = 0.8097\n", "│   │   ├── Epoch N-1      = 0.8068 (\u001b[32m↗ 0.0029\u001b[0m)\n", "│   │   └── Best until now = 0.8068 (\u001b[32m↗ 0.0029\u001b[0m)\n", "│   ├── Background_iou = 0.7447\n", "│   │   ├── Epoch N-1      = 0.736  (\u001b[32m↗ 0.0086\u001b[0m)\n", "│   │   └── Best until now = 0.736  (\u001b[32m↗ 0.0086\u001b[0m)\n", "│   └── Mean_iou = 0.7772\n", "│       ├── Epoch N-1      = 0.7714 (\u001b[32m↗ 0.0058\u001b[0m)\n", "│       └── Best until now = 0.7714 (\u001b[32m↗ 0.0058\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3024\n", "    │   ├── Epoch N-1      = 0.3035 (\u001b[32m↘ -0.0011\u001b[0m)\n", "    │   └── Best until now = 0.3035 (\u001b[32m↘ -0.0011\u001b[0m)\n", "    ├── Target_iou = 0.7624\n", "    │   ├── Epoch N-1      = 0.7617 (\u001b[32m↗ 0.0007\u001b[0m)\n", "    │   └── Best until now = 0.7617 (\u001b[32m↗ 0.0007\u001b[0m)\n", "    ├── Background_iou = 0.5596\n", "    │   ├── Epoch N-1      = 0.5569 (\u001b[32m↗ 0.0027\u001b[0m)\n", "    │   └── Best until now = 0.5569 (\u001b[32m↗ 0.0027\u001b[0m)\n", "    └── Mean_iou = 0.661\n", "        ├── Epoch N-1      = 0.6593 (\u001b[32m↗ 0.0017\u001b[0m)\n", "        └── Best until now = 0.6593 (\u001b[32m↗ 0.0017\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Train epoch 14: 100%|██████████| 309/309 [02:01<00:00,  2.54it/s, BCEDiceLoss=0.215, background_IOU=0.748, gpu_mem=1.14, mean_IOU=0.781, target_IOU=0.813]\n", "Validating epoch 14: 100%|██████████| 65/65 [00:17<00:00,  3.78it/s]\n", "[2023-11-13 11:50:45] INFO - base_sg_logger.py - Checkpoint saved in ./notebook_ckpts/segmentation_quick_start/RUN_20231113_111507_197271/ckpt_best.pth\n", "[2023-11-13 11:50:45] INFO - sg_trainer.py - Best checkpoint overriden: validation target_IOU: 0.763008713722229\n"]}, {"output_type": "stream", "name": "stdout", "text": ["===========================================================\n", "SUMMARY OF EPOCH 14\n", "├── Train\n", "│   ├── Bcediceloss = 0.2155\n", "│   │   ├── Epoch N-1      = 0.2194 (\u001b[32m↘ -0.0039\u001b[0m)\n", "│   │   └── Best until now = 0.2194 (\u001b[32m↘ -0.0039\u001b[0m)\n", "│   ├── Target_iou = 0.8134\n", "│   │   ├── Epoch N-1      = 0.8097 (\u001b[32m↗ 0.0037\u001b[0m)\n", "│   │   └── Best until now = 0.8097 (\u001b[32m↗ 0.0037\u001b[0m)\n", "│   ├── Background_iou = 0.7484\n", "│   │   ├── Epoch N-1      = 0.7447 (\u001b[32m↗ 0.0038\u001b[0m)\n", "│   │   └── Best until now = 0.7447 (\u001b[32m↗ 0.0038\u001b[0m)\n", "│   └── Mean_iou = 0.7809\n", "│       ├── Epoch N-1      = 0.7772 (\u001b[32m↗ 0.0037\u001b[0m)\n", "│       └── Best until now = 0.7772 (\u001b[32m↗ 0.0037\u001b[0m)\n", "└── Validation\n", "    ├── Bcediceloss = 0.3015\n", "    │   ├── Epoch N-1      = 0.3024 (\u001b[32m↘ -0.0009\u001b[0m)\n", "    │   └── Best until now = 0.3024 (\u001b[32m↘ -0.0009\u001b[0m)\n", "    ├── Target_iou = 0.763\n", "    │   ├── Epoch N-1      = 0.7624 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    │   └── Best until now = 0.7624 (\u001b[32m↗ 0.0006\u001b[0m)\n", "    ├── Background_iou = 0.5621\n", "    │   ├── Epoch N-1      = 0.5596 (\u001b[32m↗ 0.0025\u001b[0m)\n", "    │   └── Best until now = 0.5596 (\u001b[32m↗ 0.0025\u001b[0m)\n", "    └── Mean_iou = 0.6625\n", "        ├── Epoch N-1      = 0.661  (\u001b[32m↗ 0.0016\u001b[0m)\n", "        └── Best until now = 0.661  (\u001b[32m↗ 0.0016\u001b[0m)\n", "\n", "===========================================================\n"]}, {"output_type": "stream", "name": "stderr", "text": ["[2023-11-13 11:50:47] INFO - sg_trainer.py - RUNNING ADDITIONAL TEST ON THE AVERAGED MODEL...\n", "Validating epoch 15:  98%|█████████▊| 64/65 [00:16<00:00,  3.27it/s]"]}], "source": ["trainer.train(model=model, training_params=train_params, train_loader=train_loader, valid_loader=valid_loader)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "X8BJq1crcbjl", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "661796b8-431a-4c23-ac57-9bdc579a685d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Best Checkpoint mIoU is: 0.763008713722229\n"]}], "source": ["print(\"Best Checkpoint mIoU is: \"+ str(trainer.best_metric))"]}, {"cell_type": "markdown", "metadata": {"id": "3Nybj15cchxd"}, "source": ["Now you can download your trained weights from this directory"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "_iHsFgPSciQh"}, "outputs": [], "source": ["print(trainer.checkpoints_dir_path)"]}, {"cell_type": "markdown", "metadata": {"id": "yuhYeXLA18q5"}, "source": ["# 6. Predict\n"]}, {"cell_type": "markdown", "metadata": {"id": "VjRA1tu1mvXQ"}, "source": ["When the training is complete you can use the trained model to get predictions on the validation set, your data or some other image. Let's load some image and\n", "run a model inference to create a binary segmentation mask."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "Ads7RyGN2JwQ", "colab": {"base_uri": "https://localhost:8080/", "height": 977}, "outputId": "c99ede2d-7fdd-428a-95fe-cac9afbf508b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<PIL.Image.Image image mode=RGB size=320x480>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<PIL.Image.Image image mode=L size=320x480>"], "image/png": "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\n"}, "metadata": {}}], "source": ["from torchvision.transforms import Compose, ToTensor, Resize, Normalize, ToPILImage\n", "from PIL import Image\n", "import torch\n", "\n", "pre_proccess = Compose([\n", "    ToTensor(),\n", "    Normalize([.485, .456, .406], [.229, .224, .225])\n", "])\n", "\n", "demo_img_path = os.path.join(root_dir, \"images\", \"ache-adult-depression-expression-41253.png\")\n", "img = Image.open(demo_img_path)\n", "# Resize the image and display\n", "img = Resize(size=(480, 320))(img)\n", "display(img)\n", "\n", "# Run pre-proccess - transforms to tensor and apply normalizations.\n", "img = pre_proccess(img).unsqueeze(0).cuda()\n", "\n", "# Run inference\n", "model = trainer.net\n", "model = model.eval()\n", "mask = model(img)\n", "\n", "# Run post-proccess - apply sigmoid to output probabilities, then apply hard\n", "# threshold of 0.5 for binary mask prediction.\n", "mask = torch.sigmoid(mask).gt(0.5).squeeze()\n", "mask = ToPILImage()(mask.float())\n", "display(mask)\n"]}, {"cell_type": "markdown", "metadata": {"id": "-k6ZLKHL1hIM"}, "source": ["# 7. Convert to ONNX/TensorRT"]}, {"cell_type": "markdown", "metadata": {"id": "br7n55Szm4Nq"}, "source": ["Let's compile our model to ONNX."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "q0AGQvEf11PT", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "76b54859-3375-4fc4-c7a7-5b86ed3d80fb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ONNX successfully created at:  /content/model.onnx\n"]}], "source": ["from onnxsim import simplify\n", "import onnx\n", "\n", "onnx_path = os.path.join(os.getcwd(), \"model.onnx\")\n", "\n", "input_size = [1, 3, 480, 320]\n", "model.prep_model_for_conversion(input_size=input_size)\n", "\n", "torch.onnx.export(model,\n", "                  torch.randn(*input_size).cuda(),\n", "                  onnx_path)\n", "\n", "# onnx simplifier\n", "model_sim, check = simplify(onnx_path)\n", "assert check, \"Simplified ONNX model could not be validated\"\n", "onnx.save_model(model_sim, onnx_path)\n", "\n", "print(\"ONNX successfully created at: \", onnx_path)\n", "\n"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}